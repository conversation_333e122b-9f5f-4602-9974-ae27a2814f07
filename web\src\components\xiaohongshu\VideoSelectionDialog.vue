<template>
  <el-dialog
    title="选择要发布的视频"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose"
    class="video-selection-dialog"
  >
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          size="small"
          @click="loadVideoFiles"
          :loading="loading"
        >
          刷新列表
        </el-button>
        <el-button
          type="warning"
          size="small"
          @click="clearSelection"
          :disabled="selectedVideos.length === 0"
        >
          清空选择
        </el-button>
        <el-tag v-if="selectedVideos.length > 0" type="warning" size="small">
          限制：每个设备只能选择一个视频
        </el-tag>
      </div>
      <div class="toolbar-right">
        <span class="selection-count">
          已选择: {{ selectedVideos.length }} / {{ filteredVideoFiles.length }}
        </span>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索视频文件名"
          size="small"
          style="width: 250px; margin-left: 15px;"
          @input="filterVideoFiles"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
    </div>

    <!-- 视频选择网格 -->
    <div class="video-grid" v-loading="loading">
      <!-- 使用Element UI Card组件 -->
      <el-card
        v-for="video in filteredVideoFiles"
        :key="video.id"
        class="video-card"
        :class="{ 'selected': isSelected(video.id) }"
        :body-style="{ padding: '0px' }"
        shadow="hover"
        @click.native="toggleSelection(video)"
      >
        <!-- 选择状态 -->
        <div class="selection-indicator">
          <el-checkbox
            :value="isSelected(video.id)"
            @change="toggleSelection(video)"
            @click.stop
          ></el-checkbox>
        </div>

        <!-- 视频预览区域 -->
        <div class="video-preview-container">
          <!-- 缩略图 -->
          <div class="video-thumbnail" @click.stop="previewVideo(video)">
            <img
              v-if="video.thumbnail_path"
              :src="getThumbnailUrl(video.thumbnail_path)"
              :alt="video.original_name"
              @error="handleImageError"
            />
            <div v-else class="no-thumbnail">
              <i class="el-icon-video-camera"></i>
              <span>无预览</span>
            </div>

            <!-- 视频时长 -->
            <div v-if="video.video_duration > 0" class="duration-badge">
              {{ formatDuration(video.video_duration) }}
            </div>

            <!-- 播放按钮 -->
            <div class="play-overlay">
              <el-button
                type="primary"
                icon="el-icon-video-play"
                circle
                size="large"
                class="play-button"
              ></el-button>
            </div>

            <!-- 选择状态覆盖层 -->
            <div v-if="isSelected(video.id)" class="selected-overlay">
              <el-button
                type="success"
                icon="el-icon-check"
                circle
                size="large"
                class="selected-button"
              ></el-button>
            </div>
          </div>
        </div>

        <!-- 视频信息 -->
        <div class="video-info" style="padding: 12px;">
          <div class="video-title">
            <el-tooltip :content="video.original_name" placement="top">
              <span>{{ video.original_name }}</span>
            </el-tooltip>
          </div>

          <div class="video-meta">
            <el-tag size="mini" type="info">{{ formatFileSize(video.file_size) }}</el-tag>
            <el-tag size="mini" type="success">{{ video.video_format.toUpperCase() }}</el-tag>
            <el-tag v-if="video.video_resolution" size="mini" type="warning">{{ video.video_resolution }}</el-tag>
          </div>

          <div class="video-stats">
            <div class="stat-item">
              <i class="el-icon-time"></i>
              <span>{{ formatTime(video.upload_time) }}</span>
            </div>
          </div>

          <!-- 传输信息按钮 -->
          <div class="video-actions" style="margin-top: 8px;">
            <el-button-group>
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-view"
                @click.stop="previewVideo(video)"
              >
                预览
              </el-button>
              <el-button
                size="mini"
                type="info"
                icon="el-icon-data-line"
                @click.stop="showTransferInfo(video)"
              >
                传输信息
              </el-button>
            </el-button-group>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="pagination.total > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[12, 24, 48, 96]"
        :page-size="pagination.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      >
      </el-pagination>
    </div>

    <!-- 底部操作栏 -->
    <div slot="footer" class="dialog-footer">
      <div class="footer-info">
        <span>已选择 {{ selectedVideos.length }} 个视频文件</span>
        <span v-if="selectedVideos.length > 0" class="total-size">
          总大小: {{ formatFileSize(totalSelectedSize) }}
        </span>
      </div>
      <div class="footer-actions">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="confirmSelection"
          :disabled="selectedVideos.length === 0"
        >
          确定选择 ({{ selectedVideos.length }})
        </el-button>
      </div>
    </div>

    <!-- 视频预览对话框 -->
    <el-dialog
      title="视频预览"
      :visible.sync="previewDialogVisible"
      width="70%"
      :append-to-body="true"
    >
      <div v-if="currentPreviewVideo" class="video-preview">
        <div class="preview-video">
          <video
            ref="previewPlayer"
            :src="getVideoUrl(currentPreviewVideo.file_path)"
            controls
            preload="metadata"
            style="width: 100%; max-height: 400px;"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
        <div class="preview-info">
          <h3>{{ currentPreviewVideo.original_name }}</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>文件大小:</label>
              <span>{{ formatFileSize(currentPreviewVideo.file_size) }}</span>
            </div>
            <div class="info-item">
              <label>视频格式:</label>
              <span>{{ currentPreviewVideo.video_format.toUpperCase() }}</span>
            </div>
            <div class="info-item">
              <label>视频时长:</label>
              <span>{{ formatDuration(currentPreviewVideo.video_duration) }}</span>
            </div>
            <div class="info-item">
              <label>分辨率:</label>
              <span>{{ currentPreviewVideo.video_resolution || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>上传时间:</label>
              <span>{{ formatTime(currentPreviewVideo.upload_time) }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import axios from 'axios'
import { getApiBaseUrl } from '@/utils/serverConfig'

export default {
  name: 'VideoSelectionDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    preSelectedVideos: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      videoFiles: [],
      filteredVideoFiles: [],
      selectedVideos: [],
      searchKeyword: '',
      loading: false,
      previewDialogVisible: false,
      currentPreviewVideo: null,
      pagination: {
        page: 1,
        limit: 24,
        total: 0,
        totalPages: 0
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    totalSelectedSize() {
      return this.selectedVideos.reduce((total, video) => total + video.file_size, 0)
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadVideoFiles()
        this.selectedVideos = [...this.preSelectedVideos]
      }
    },
    preSelectedVideos: {
      handler(newVal) {
        this.selectedVideos = [...newVal]
      },
      deep: true
    }
  },
  methods: {
    // 获取API基础URL
    getApiBaseUrl() {
      return getApiBaseUrl()
    },

    async loadVideoFiles() {
      this.loading = true
      try {
        const response = await axios.get('/api/xiaohongshu/video-files', {
          params: {
            page: this.pagination.page,
            limit: this.pagination.limit,
            status: 'active'
          }
        })

        if (response.data.success) {
          this.videoFiles = response.data.data.videos
          this.pagination = response.data.data.pagination
          this.filterVideoFiles()
        }
      } catch (error) {
        console.error('加载视频文件列表失败:', error)
        this.$message.error('加载视频文件列表失败')
      } finally {
        this.loading = false
      }
    },

    filterVideoFiles() {
      if (!this.searchKeyword.trim()) {
        this.filteredVideoFiles = this.videoFiles
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredVideoFiles = this.videoFiles.filter(video =>
          video.original_name.toLowerCase().includes(keyword)
        )
      }
    },

    isSelected(videoId) {
      return this.selectedVideos.some(video => video.id === videoId)
    },

    toggleSelection(video) {
      const index = this.selectedVideos.findIndex(v => v.id === video.id)
      if (index > -1) {
        // 取消选择
        this.selectedVideos.splice(index, 1)
      } else {
        // 限制：每个设备只能选择一个视频
        if (this.selectedVideos.length >= 1) {
          this.$message.warning('每个设备只能选择一个视频进行发布')
          return
        }
        this.selectedVideos.push(video)
      }
    },

    clearSelection() {
      this.selectedVideos = []
    },

    confirmSelection() {
      this.$emit('videos-selected', this.selectedVideos)
      this.handleClose()
    },

    handleClose() {
      this.visible = false
    },

    handleSizeChange(val) {
      this.pagination.limit = val
      this.pagination.page = 1
      this.loadVideoFiles()
    },

    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadVideoFiles()
    },

    handleImageError(event) {
      try {
        if (event && event.target) {
          event.target.style.display = 'none'

          // 安全地查找父节点和no-thumbnail元素
          const parentNode = event.target.parentNode
          if (parentNode) {
            const noThumbnailElement = parentNode.querySelector('.no-thumbnail')
            if (noThumbnailElement) {
              noThumbnailElement.style.display = 'flex'
            }
          }
        }
      } catch (error) {
        console.warn('处理图片错误时出现异常:', error)
      }
    },

    previewVideo(video) {
      this.currentPreviewVideo = video
      this.previewDialogVisible = true

      // 等待对话框打开后再加载视频
      this.$nextTick(() => {
        if (this.$refs.previewPlayer) {
          this.$refs.previewPlayer.load()
        }
      })
    },

    // 显示传输信息
    showTransferInfo(video) {
      console.log('🎬 VideoSelectionDialog: 显示传输信息', video)
      console.log('🎬 VideoSelectionDialog: 视频ID:', video.id, '视频名称:', video.original_name)

      // 直接调用父组件的方法
      if (this.$parent && typeof this.$parent.showVideoTransferInfo === 'function') {
        console.log('✅ VideoSelectionDialog: 找到父组件的showVideoTransferInfo方法，直接调用')
        this.$parent.showVideoTransferInfo(video)
        return
      }

      // 如果直接调用失败，尝试事件方式
      console.log('🔍 VideoSelectionDialog: 直接调用失败，尝试事件方式')
      this.$emit('show-transfer-info', video)
      console.log('📤 VideoSelectionDialog: 已发送show-transfer-info事件')
    },

    getVideoUrl(filePath) {
      if (!filePath) return ''

      // 如果已经是完整URL，直接返回
      if (filePath.startsWith('http')) {
        return filePath
      }

      // 如果是相对路径，转换为绝对路径
      if (filePath.startsWith('/uploads/')) {
        return `${this.getApiBaseUrl()}${filePath}`
      }

      // 从完整路径中提取文件名
      const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || filePath
      return `${this.getApiBaseUrl()}/uploads/videos/${fileName}`
    },

    getThumbnailUrl(thumbnailPath) {
      if (!thumbnailPath) return ''
      return `${this.getApiBaseUrl()}${thumbnailPath}`
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    formatDuration(seconds) {
      if (!seconds) return '0:00'
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins}:${secs.toString().padStart(2, '0')}`
    },

    formatTime(timeString) {
      if (!timeString) return '-'
      return new Date(timeString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.video-selection-dialog {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }

  .selection-count {
    font-weight: 500;
    color: #409eff;
  }

  .video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    min-height: 400px;
  }

  .video-card {
    position: relative;
    transition: all 0.3s;
    cursor: pointer;

    &.selected {
      border: 2px solid #409eff !important;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
    }
  }

  .selection-indicator {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px;
  }

  /* 视频预览容器 */
  .video-preview-container {
    position: relative;
    width: 100%;
    height: 200px;
    background: #f5f7fa;
  }

  .video-thumbnail {
    position: relative;
    width: 100%;
    height: 160px;
    background: #f5f7fa;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.02);

      .play-overlay {
        opacity: 1;
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .no-thumbnail {
      display: none;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #909399;

      i {
        font-size: 32px;
        margin-bottom: 8px;
      }
    }

    .play-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
      opacity: 0;
      transition: opacity 0.2s;
    }

    .duration-badge {
      position: absolute;
      bottom: 8px;
      right: 8px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
    }

    .selected-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(64, 158, 255, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 32px;
        color: white;
        background: #409eff;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .video-info {
    padding: 12px;

    .video-title {
      font-weight: 500;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .video-meta {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
      font-size: 12px;
      color: #909399;

      span {
        background: #f0f2f5;
        padding: 2px 6px;
        border-radius: 3px;
      }
    }

    .video-stats {
      font-size: 11px;
      color: #c0c4cc;
    }
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .footer-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .total-size {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }

    .footer-actions {
      display: flex;
      gap: 10px;
    }
  }

  .video-preview {
    .preview-video {
      margin-bottom: 20px;
      text-align: center;
    }

    .preview-info {
      h3 {
        margin: 0 0 15px 0;
        color: #303133;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;

        .info-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid #ebeef5;

          label {
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
          }
        }
      }
    }
  }
}
</style>
