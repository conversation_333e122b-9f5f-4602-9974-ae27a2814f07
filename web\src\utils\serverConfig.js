/**
 * 服务器配置工具
 * 用于动态获取服务器地址，解决跨域和硬编码IP地址问题
 */

/**
 * 获取服务器URL
 * @returns {string} 服务器URL
 */
export function getServerUrl() {
  const currentPort = window.location.port
  const currentHostname = window.location.hostname

  console.log('🔧 [ServerConfig] 获取服务器URL，当前环境:', {
    NODE_ENV: process.env.NODE_ENV,
    hostname: currentHostname,
    port: currentPort,
    protocol: window.location.protocol,
    fullUrl: window.location.href
  })

  // 检查是否为Vue开发服务器端口（8080或8081）或localhost访问
  const isDevServerPort = currentPort === '8080' || currentPort === '8081'
  const isLocalhostAccess = currentHostname === 'localhost' || currentHostname === '127.0.0.1'

  // 在开发环境中，如果是通过Vue开发服务器访问，使用代理
  if (process.env.NODE_ENV === 'development' && isDevServerPort) {
    console.log('🔧 [ServerConfig] 使用开发代理模式 (端口:', currentPort, ')')
    return '' // 使用相对路径，通过代理访问
  }

  // 特殊处理：如果当前端口是8080/8081但不是开发环境，仍然使用代理
  // 这种情况可能发生在构建后的代码中NODE_ENV没有正确设置
  if (isDevServerPort) {
    console.log('🔧 [ServerConfig] 检测到开发服务器端口，强制使用代理模式')
    return '' // 使用相对路径，通过代理访问
  }

  // 如果是localhost访问但端口是3002，直接返回完整URL
  if (isLocalhostAccess && currentPort === '3002') {
    const serverUrl = `${window.location.protocol}//${currentHostname}:${currentPort}`
    console.log('🔧 [ServerConfig] 检测到localhost:3002直接访问，返回完整URL:', serverUrl)
    return serverUrl
  }

  // 如果是其他localhost访问，使用代理模式
  if (isLocalhostAccess) {
    console.log('🔧 [ServerConfig] 检测到localhost访问，使用代理模式')
    return '' // 使用相对路径，通过代理访问
  }

  // 在生产环境或直接访问时，使用当前页面的协议和主机
  const protocol = window.location.protocol === 'https:' ? 'https' : 'http'
  const hostname = currentHostname
  const port = currentPort || (protocol === 'https' ? '443' : '80')

  // 如果端口是3002，直接使用；否则假设服务器在3002端口
  const serverPort = port === '3002' ? port : '3002'

  const serverUrl = `${protocol}://${hostname}:${serverPort}`
  console.log('🔧 [ServerConfig] 生成的服务器URL:', serverUrl)

  return serverUrl
}

/**
 * 获取WebSocket服务器URL
 * @returns {string} WebSocket服务器URL
 */
export function getWebSocketUrl() {
  return getServerUrl()
}

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
export function getApiBaseUrl() {
  return getServerUrl()
}

/**
 * 检查是否为开发环境
 * @returns {boolean} 是否为开发环境
 */
export function isDevelopment() {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检查是否通过代理访问
 * @returns {boolean} 是否通过代理访问
 */
export function isProxyAccess() {
  return isDevelopment() && window.location.hostname === 'localhost'
}

/**
 * 获取当前环境信息
 * @returns {object} 环境信息
 */
export function getEnvironmentInfo() {
  return {
    isDevelopment: isDevelopment(),
    isProxyAccess: isProxyAccess(),
    hostname: window.location.hostname,
    port: window.location.port,
    protocol: window.location.protocol,
    serverUrl: getServerUrl(),
    webSocketUrl: getWebSocketUrl(),
    apiBaseUrl: getApiBaseUrl()
  }
}

// 在控制台输出环境信息（仅开发环境）
if (isDevelopment()) {
  console.log('🔧 服务器配置信息:', getEnvironmentInfo())
}
