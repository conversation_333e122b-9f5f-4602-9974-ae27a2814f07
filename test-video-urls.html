<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频URL测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .url-test {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 3px;
        }
        .url-test a {
            color: #007bff;
            text-decoration: none;
            word-break: break-all;
        }
        .url-test a:hover {
            text-decoration: underline;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: auto;
            margin: 10px 0;
        }
        .thumbnail {
            max-width: 200px;
            height: auto;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频URL测试页面</h1>
        <p>测试视频文件和缩略图的访问情况</p>

        <div class="test-section">
            <h3>📹 视频文件测试</h3>
            <div class="url-test">
                <strong>测试视频1:</strong><br>
                <a href="http://localhost:3002/uploads/videos/video-1754998361117-259859642.mp4" target="_blank">
                    http://localhost:3002/uploads/videos/video-1754998361117-259859642.mp4
                </a>
                <span class="status" id="video1-status">检测中...</span>
                <br>
                <video controls preload="metadata" id="video1">
                    <source src="http://localhost:3002/uploads/videos/video-1754998361117-259859642.mp4" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
            </div>

            <div class="url-test">
                <strong>测试视频2:</strong><br>
                <a href="http://localhost:3002/uploads/videos/video-1754998453765-64244105.mp4" target="_blank">
                    http://localhost:3002/uploads/videos/video-1754998453765-64244105.mp4
                </a>
                <span class="status" id="video2-status">检测中...</span>
                <br>
                <video controls preload="metadata" id="video2">
                    <source src="http://localhost:3002/uploads/videos/video-1754998453765-64244105.mp4" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
            </div>
        </div>

        <div class="test-section">
            <h3>🖼️ 缩略图测试</h3>
            <div class="url-test">
                <strong>测试缩略图1:</strong><br>
                <a href="http://localhost:3002/thumbnails/thumb_video-1754998361117-259859642.svg" target="_blank">
                    http://localhost:3002/thumbnails/thumb_video-1754998361117-259859642.svg
                </a>
                <span class="status" id="thumb1-status">检测中...</span>
                <br>
                <img class="thumbnail" src="http://localhost:3002/thumbnails/thumb_video-1754998361117-259859642.svg" 
                     alt="缩略图1" id="thumb1" onerror="this.style.display='none'">
            </div>

            <div class="url-test">
                <strong>测试缩略图2:</strong><br>
                <a href="http://localhost:3002/thumbnails/thumb_video-1754998453765-64244105.svg" target="_blank">
                    http://localhost:3002/thumbnails/thumb_video-1754998453765-64244105.svg
                </a>
                <span class="status" id="thumb2-status">检测中...</span>
                <br>
                <img class="thumbnail" src="http://localhost:3002/thumbnails/thumb_video-1754998453765-64244105.svg" 
                     alt="缩略图2" id="thumb2" onerror="this.style.display='none'">
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 调试信息</h3>
            <div id="debug-info">
                <p><strong>当前页面URL:</strong> <span id="current-url"></span></p>
                <p><strong>服务器基础URL:</strong> <span id="server-url"></span></p>
                <p><strong>测试时间:</strong> <span id="test-time"></span></p>
            </div>
        </div>
    </div>

    <script>
        // 更新调试信息
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('server-url').textContent = 'http://localhost:3002';
        document.getElementById('test-time').textContent = new Date().toLocaleString();

        // 测试视频加载状态
        function testVideo(videoId, statusId) {
            const video = document.getElementById(videoId);
            const status = document.getElementById(statusId);
            
            video.addEventListener('loadedmetadata', () => {
                status.textContent = '✅ 加载成功';
                status.className = 'status success';
                console.log(`${videoId} 加载成功:`, {
                    duration: video.duration,
                    videoWidth: video.videoWidth,
                    videoHeight: video.videoHeight
                });
            });
            
            video.addEventListener('error', (e) => {
                status.textContent = '❌ 加载失败';
                status.className = 'status error';
                console.error(`${videoId} 加载失败:`, e);
            });
        }

        // 测试缩略图加载状态
        function testThumbnail(thumbId, statusId) {
            const thumb = document.getElementById(thumbId);
            const status = document.getElementById(statusId);
            
            thumb.addEventListener('load', () => {
                status.textContent = '✅ 加载成功';
                status.className = 'status success';
                console.log(`${thumbId} 加载成功`);
            });
            
            thumb.addEventListener('error', (e) => {
                status.textContent = '❌ 加载失败';
                status.className = 'status error';
                console.error(`${thumbId} 加载失败:`, e);
            });
        }

        // 启动测试
        testVideo('video1', 'video1-status');
        testVideo('video2', 'video2-status');
        testThumbnail('thumb1', 'thumb1-status');
        testThumbnail('thumb2', 'thumb2-status');

        // 网络请求测试
        async function testUrls() {
            const urls = [
                'http://localhost:3002/uploads/videos/video-1754998361117-259859642.mp4',
                'http://localhost:3002/uploads/videos/video-1754998453765-64244105.mp4',
                'http://localhost:3002/thumbnails/thumb_video-1754998361117-259859642.svg',
                'http://localhost:3002/thumbnails/thumb_video-1754998453765-64244105.svg'
            ];

            for (const url of urls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    console.log(`URL测试 ${url}:`, {
                        status: response.status,
                        statusText: response.statusText,
                        contentType: response.headers.get('content-type'),
                        contentLength: response.headers.get('content-length')
                    });
                } catch (error) {
                    console.error(`URL测试失败 ${url}:`, error);
                }
            }
        }

        // 延迟执行URL测试
        setTimeout(testUrls, 1000);
    </script>
</body>
</html>
