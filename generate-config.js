#!/usr/bin/env node

/**
 * Auto.js云群控系统配置生成工具
 * 自动生成.env配置文件和相关配置
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// 生成随机JWT密钥
function generateJWTSecret() {
  return crypto.randomBytes(64).toString('hex');
}

// 生成强密码
function generatePassword(length = 16) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

// 验证IP地址格式
function isValidIP(ip) {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
}

// 主配置生成函数
async function generateConfig() {
  try {
    log('=== Auto.js云群控系统配置生成工具 ===', 'blue');
    console.log();
    
    // 基础配置
    let publicIP;
    do {
      publicIP = await question('请输入服务器公网IP地址: ');
      if (!publicIP) {
        log('公网IP不能为空', 'red');
        continue;
      }
      if (!isValidIP(publicIP)) {
        log('请输入有效的IP地址格式', 'red');
        continue;
      }
      break;
    } while (true);
    
    const environment = await question('请选择环境 (development/production) [production]: ') || 'production';
    
    console.log();
    log('--- 本地数据库配置 ---', 'cyan');
    const localDbHost = await question('本地数据库主机 [localhost]: ') || 'localhost';
    const localDbUser = await question('本地数据库用户名 [autojs_control]: ') || 'autojs_control';
    let localDbPassword;
    do {
      localDbPassword = await question('本地数据库密码: ');
      if (!localDbPassword) {
        log('数据库密码不能为空', 'red');
        continue;
      }
      break;
    } while (true);
    const localDbName = await question('本地数据库名 [autojs_control]: ') || 'autojs_control';
    
    console.log();
    log('--- 主站数据库配置（可选，用于账号验证）---', 'cyan');
    const useMainDb = await question('是否配置主站数据库？(y/N): ');
    let mainDbHost = 'localhost';
    let mainDbUser = 'root';
    let mainDbPassword = 'root';
    let mainDbName = 'zhuzhan';
    
    if (useMainDb.toLowerCase() === 'y') {
      mainDbHost = await question('主站数据库主机: ') || 'localhost';
      mainDbUser = await question('主站数据库用户名: ') || 'root';
      mainDbPassword = await question('主站数据库密码: ') || 'root';
      mainDbName = await question('主站数据库名 [zhuzhan]: ') || 'zhuzhan';
    }
    
    console.log();
    log('--- 安全配置 ---', 'cyan');
    const jwtSecret = generateJWTSecret();
    log(`JWT密钥已自动生成: ${jwtSecret.substring(0, 20)}...`, 'green');
    
    // 生成.env文件内容
    const envContent = generateEnvContent({
      environment,
      publicIP,
      localDbHost,
      localDbUser,
      localDbPassword,
      localDbName,
      mainDbHost,
      mainDbUser,
      mainDbPassword,
      mainDbName,
      jwtSecret
    });
    
    // 写入.env文件
    fs.writeFileSync('.env', envContent);
    log('✅ .env 配置文件已生成', 'green');
    
    // 创建统一配置模块
    createConfigModule();
    
    // 更新数据库配置文件
    updateDatabaseConfig();
    
    // 更新设备端脚本
    updateDeviceScript(publicIP);
    
    // 生成PM2配置
    generatePM2Config();
    
    // 生成Nginx配置
    generateNginxConfig(publicIP);
    
    // 生成部署脚本
    generateDeployScript(publicIP);
    
    console.log();
    log('=== 配置生成完成 ===', 'green');
    log(`服务器地址: http://${publicIP}`, 'cyan');
    log(`API地址: http://${publicIP}:3002`, 'cyan');
    console.log();
    log('下一步操作：', 'yellow');
    log('1. 检查并调整 .env 文件中的配置');
    log('2. 运行 npm install 安装依赖');
    log('3. 运行 node server/config/database.js 初始化数据库');
    log('4. 运行 ./deploy.sh 或手动部署服务');
    console.log();
    
  } catch (error) {
    log(`配置生成过程中发生错误: ${error.message}`, 'red');
  } finally {
    rl.close();
  }
}

// 生成.env文件内容
function generateEnvContent(config) {
  return `# =====================================================
# Auto.js云群控系统 - 环境配置文件
# 自动生成于 ${new Date().toISOString()}
# =====================================================

# 服务器配置
NODE_ENV=${config.environment}
SERVER_PORT=3002
SERVER_HOST=${config.publicIP}
PUBLIC_IP=${config.publicIP}

# JWT配置
JWT_SECRET=${config.jwtSecret}
JWT_EXPIRES_IN=24h

# 本地数据库配置（现有系统）
LOCAL_DB_HOST=${config.localDbHost}
LOCAL_DB_PORT=3306
LOCAL_DB_USER=${config.localDbUser}
LOCAL_DB_PASSWORD=${config.localDbPassword}
LOCAL_DB_NAME=${config.localDbName}

# 主站数据库配置（用于账号验证）
MAIN_DB_HOST=${config.mainDbHost}
MAIN_DB_PORT=3306
MAIN_DB_USER=${config.mainDbUser}
MAIN_DB_PASSWORD=${config.mainDbPassword}
MAIN_DB_NAME=${config.mainDbName}

# 数据库连接池配置
DB_CONNECTION_LIMIT=10
DB_QUEUE_LIMIT=0
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# 系统配置
DEFAULT_TRIAL_DAYS=7
MAX_DEVICES_PER_USER=50
ENABLE_REGISTRATION=false
MAINTENANCE_MODE=false

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# 文件上传配置
UPLOAD_MAX_SIZE=104857600
UPLOAD_PATH=./uploads

# 缓存配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=

# 安全配置
BCRYPT_ROUNDS=10
SESSION_TIMEOUT=86400
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# WebSocket配置
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000
WS_MAX_HTTP_BUFFER_SIZE=100000000

# 设备连接配置
DEVICE_RECONNECTION_COOLDOWN=10000
DEVICE_MAX_RECONNECTION_ATTEMPTS=5
DEVICE_RECONNECTION_DELAY=2000
`;
}

// 创建统一配置模块
function createConfigModule() {
  const configDir = 'config';
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }
  
  const configContent = `const path = require('path');
const fs = require('fs');

// 加载环境变量
require('dotenv').config();

// 配置验证函数
function validateConfig() {
  const required = [
    'SERVER_HOST',
    'LOCAL_DB_PASSWORD',
    'JWT_SECRET'
  ];
  
  const missing = required.filter(key => !process.env[key]);
  if (missing.length > 0) {
    throw new Error(\`缺少必需的环境变量: \${missing.join(', ')}\`);
  }
}

// 统一配置对象
const config = {
  // 环境配置
  env: process.env.NODE_ENV || 'development',
  
  // 服务器配置
  server: {
    host: process.env.SERVER_HOST || 'localhost',
    port: parseInt(process.env.SERVER_PORT) || 3002,
    publicIP: process.env.PUBLIC_IP || process.env.SERVER_HOST || 'localhost'
  },
  
  // 数据库配置
  database: {
    local: {
      host: process.env.LOCAL_DB_HOST || 'localhost',
      port: parseInt(process.env.LOCAL_DB_PORT) || 3306,
      user: process.env.LOCAL_DB_USER || 'autojs_control',
      password: process.env.LOCAL_DB_PASSWORD || 'root',
      database: process.env.LOCAL_DB_NAME || 'autojs_control',
      charset: 'utf8mb4',
      timezone: '+08:00',
      connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
      queueLimit: parseInt(process.env.DB_QUEUE_LIMIT) || 0,
      acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
      timeout: parseInt(process.env.DB_TIMEOUT) || 60000
    },
    
    main: {
      host: process.env.MAIN_DB_HOST || 'localhost',
      port: parseInt(process.env.MAIN_DB_PORT) || 3306,
      user: process.env.MAIN_DB_USER || 'root',
      password: process.env.MAIN_DB_PASSWORD || 'root',
      database: process.env.MAIN_DB_NAME || 'zhuzhan',
      charset: 'utf8mb4',
      timezone: '+08:00',
      connectionLimit: 5,
      queueLimit: 0,
      acquireTimeout: 60000,
      timeout: 60000
    }
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },
  
  // 系统配置
  system: {
    defaultTrialDays: parseInt(process.env.DEFAULT_TRIAL_DAYS) || 7,
    maxDevicesPerUser: parseInt(process.env.MAX_DEVICES_PER_USER) || 50,
    enableRegistration: process.env.ENABLE_REGISTRATION === 'true',
    maintenanceMode: process.env.MAINTENANCE_MODE === 'true'
  },
  
  // 文件上传配置
  upload: {
    maxSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 104857600, // 100MB
    path: process.env.UPLOAD_PATH || './uploads'
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/app.log'
  },
  
  // WebSocket配置
  websocket: {
    pingTimeout: parseInt(process.env.WS_PING_TIMEOUT) || 60000,
    pingInterval: parseInt(process.env.WS_PING_INTERVAL) || 25000,
    maxHttpBufferSize: parseInt(process.env.WS_MAX_HTTP_BUFFER_SIZE) || 100000000,
    cors: {
      origin: "*",
      methods: ["GET", "POST"],
      credentials: true
    }
  },
  
  // 设备连接配置
  device: {
    reconnectionCooldown: parseInt(process.env.DEVICE_RECONNECTION_COOLDOWN) || 10000,
    maxReconnectionAttempts: parseInt(process.env.DEVICE_MAX_RECONNECTION_ATTEMPTS) || 5,
    reconnectionDelay: parseInt(process.env.DEVICE_RECONNECTION_DELAY) || 2000
  },
  
  // 安全配置
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 10,
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT) || 86400,
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5,
    lockoutDuration: parseInt(process.env.LOCKOUT_DURATION) || 900
  }
};

// 生产环境时验证配置
if (config.env === 'production') {
  validateConfig();
}

module.exports = config;`;

  fs.writeFileSync(path.join(configDir, 'index.js'), configContent);
  log('✅ 统一配置模块已创建', 'green');
}

// 更新数据库配置文件
function updateDatabaseConfig() {
  const dbConfigPath = 'server/config/database.js';
  if (!fs.existsSync('server/config')) {
    fs.mkdirSync('server/config', { recursive: true });
  }

  const dbConfigContent = `const mysql = require('mysql2/promise');
const config = require('../../config');

// 本地数据库配置
const localDbConfig = {
  host: config.database.local.host,
  port: config.database.local.port,
  user: config.database.local.user,
  password: config.database.local.password,
  database: config.database.local.database,
  charset: config.database.local.charset,
  timezone: config.database.local.timezone
};

// 主站数据库配置
const mainDbConfig = {
  host: config.database.main.host,
  port: config.database.main.port,
  user: config.database.main.user,
  password: config.database.main.password,
  database: config.database.main.database,
  charset: config.database.main.charset,
  timezone: config.database.main.timezone
};

// 创建本地数据库连接池
const localPool = mysql.createPool({
  ...localDbConfig,
  waitForConnections: true,
  connectionLimit: config.database.local.connectionLimit,
  queueLimit: config.database.local.queueLimit,
  acquireTimeout: config.database.local.acquireTimeout,
  timeout: config.database.local.timeout,
  reconnect: true
});

// 创建主站数据库连接池
const mainPool = mysql.createPool({
  ...mainDbConfig,
  waitForConnections: true,
  connectionLimit: config.database.main.connectionLimit,
  queueLimit: config.database.main.queueLimit,
  acquireTimeout: config.database.main.acquireTimeout,
  timeout: config.database.main.timeout,
  reconnect: true
});

// 数据库初始化函数（保持原有逻辑）
async function initDatabase() {
  try {
    console.log('🔧 初始化数据库...');
    const dbConnection = await localPool.getConnection();

    // 这里包含原有的数据库初始化SQL逻辑
    // 由于篇幅限制，请参考原始文件中的initDatabase函数

    dbConnection.release();
    console.log('✅ 数据库初始化完成');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  }
}

// 数据库连接测试函数
async function testDatabaseConnections() {
  console.log('🔍 测试数据库连接...');

  try {
    const localConnection = await localPool.getConnection();
    console.log('✅ 本地数据库连接成功');
    localConnection.release();
  } catch (error) {
    console.error('❌ 本地数据库连接失败:', error.message);
  }

  try {
    const mainConnection = await mainPool.getConnection();
    console.log('✅ 主站数据库连接成功');
    mainConnection.release();
  } catch (error) {
    console.error('❌ 主站数据库连接失败:', error.message);
    console.log('💡 提示：如果主站数据库未配置，这是正常的。系统将使用本地认证模式。');
  }
}

// 导出数据库连接池和配置
module.exports = {
  // 向后兼容
  pool: localPool,
  initDatabase,
  dbConfig: localDbConfig,

  // 新的双数据库支持
  localPool,
  mainPool,

  // 配置信息
  localDbConfig,
  mainDbConfig,

  // 工具函数
  testDatabaseConnections
};`;

  fs.writeFileSync(dbConfigPath, dbConfigContent);
  log('✅ 数据库配置文件已更新', 'green');
}

// 更新设备端脚本
function updateDeviceScript(publicIP) {
  const scriptPath = 'scripts/双向.js';
  if (fs.existsSync(scriptPath)) {
    let content = fs.readFileSync(scriptPath, 'utf8');

    // 替换默认服务器地址
    const serverUrlPattern = /text=".*?:\d+"/g;
    content = content.replace(serverUrlPattern, `text="${publicIP}:3002"`);

    // 替换硬编码的IP地址
    const hardcodedIPPattern = /192\.168\.1\.91/g;
    content = content.replace(hardcodedIPPattern, publicIP);

    // 添加配置块（如果不存在）
    const configBlock = `// 服务器配置 - 自动生成于 ${new Date().toISOString()}
var SERVER_CONFIG = {
    host: "${publicIP}",
    port: 3002,
    url: "http://${publicIP}:3002"
};

console.log("服务器配置:", SERVER_CONFIG);

`;

    if (!content.includes('SERVER_CONFIG')) {
      content = configBlock + content;
    }

    fs.writeFileSync(scriptPath, content);
    log('✅ 设备端脚本配置已更新', 'green');
  } else {
    log('⚠️ 设备端脚本文件不存在，跳过更新', 'yellow');
  }
}

// 生成PM2配置
function generatePM2Config() {
  const pm2Config = `module.exports = {
  apps: [{
    name: 'autojs-control',
    script: 'server/server-main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production'
    },
    error_file: '/var/log/autojs-control-error.log',
    out_file: '/var/log/autojs-control-out.log',
    log_file: '/var/log/autojs-control.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    restart_delay: 4000,
    kill_timeout: 5000
  }]
};`;

  fs.writeFileSync('ecosystem.config.js', pm2Config);
  log('✅ PM2配置文件已生成', 'green');
}

// 生成Nginx配置
function generateNginxConfig(publicIP) {
  const nginxConfig = `server {
    listen 80;
    server_name ${publicIP};

    # 前端静态文件
    location / {
        root /var/www/autojs-control;
        index index.html;
        try_files $uri $uri/ /index.html;

        # 缓存静态资源
        location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理到Node.js
    location /api/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件上传大小限制
    client_max_body_size 100M;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy strict-origin-when-cross-origin;
}`;

  fs.writeFileSync('nginx-autojs-control.conf', nginxConfig);
  log('✅ Nginx配置文件已生成', 'green');
}

// 生成部署脚本
function generateDeployScript(publicIP) {
  const deployScript = `#!/bin/bash

# Auto.js云群控系统部署脚本
# 自动生成于 ${new Date().toISOString()}

set -e

PUBLIC_IP="${publicIP}"

echo "开始部署Auto.js云群控系统..."
echo "服务器IP: $PUBLIC_IP"

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未安装Node.js"
    exit 1
fi

# 检查PM2
if ! command -v pm2 &> /dev/null; then
    echo "安装PM2..."
    npm install -g pm2
fi

# 安装依赖
echo "安装后端依赖..."
npm install --production

# 构建前端
if [ -d "web" ]; then
    echo "构建前端..."
    cd web
    npm install
    npm run build
    cd ..

    # 部署前端
    echo "部署前端..."
    sudo mkdir -p /var/www/autojs-control
    sudo cp -r web/dist/* /var/www/autojs-control/
    sudo chown -R www-data:www-data /var/www/autojs-control 2>/dev/null || sudo chown -R nginx:nginx /var/www/autojs-control
    sudo chmod -R 755 /var/www/autojs-control
fi

# 初始化数据库
echo "初始化数据库..."
node server/config/database.js

# 配置Nginx
if [ -f "nginx-autojs-control.conf" ]; then
    echo "配置Nginx..."
    sudo cp nginx-autojs-control.conf /etc/nginx/sites-available/autojs-control 2>/dev/null || sudo cp nginx-autojs-control.conf /etc/nginx/conf.d/autojs-control.conf

    if [ -d "/etc/nginx/sites-enabled" ]; then
        sudo ln -sf /etc/nginx/sites-available/autojs-control /etc/nginx/sites-enabled/
    fi

    sudo nginx -t && sudo systemctl reload nginx
fi

# 启动后端服务
echo "启动后端服务..."
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup

echo "部署完成！"
echo "前端地址: http://$PUBLIC_IP"
echo "API地址: http://$PUBLIC_IP:3002"
echo "默认账户: admin / admin123"
echo ""
echo "请立即登录系统修改默认密码！"
`;

  fs.writeFileSync('deploy.sh', deployScript);
  fs.chmodSync('deploy.sh', '755');
  log('✅ 部署脚本已生成', 'green');
}

// 主函数执行
if (require.main === module) {
  generateConfig().catch(console.error);
}

module.exports = { generateConfig };
