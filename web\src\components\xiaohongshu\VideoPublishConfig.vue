<template>
  <div class="video-publish-config">
    <el-card>
      <div slot="header" class="card-header">
        <span>小红书发布视频配置</span>
        <!-- 头部执行按钮已删除，避免重复 -->
      </div>

      <!-- 脚本执行状态 -->
      <div v-if="isScriptRunning || isScriptCompleted" class="execution-status">
        <el-alert
          :title="currentStatus"
          :type="isScriptCompleted ? 'success' : 'info'"
          :closable="false"
          show-icon
        >
          <div>
            <p>已发布视频: {{ publishedVideoCount }} / {{ totalVideoCount }}</p>
            <p>当前步骤: {{ currentStep }}</p>
            <p v-if="errorMessage" style="color: #F56C6C;">错误信息: {{ errorMessage }}</p>
          </div>
        </el-alert>
      </div>

      <!-- 视频选择区域 -->
      <div class="video-selection-section">
        <h4>选择要发布的视频</h4>
        <div class="video-actions">
          <el-button
            type="primary"
            icon="el-icon-view"
            @click="showVideoSelectionDialog"
            :disabled="selectedVideos.length >= 1"
          >
            {{ selectedVideos.length >= 1 ? '已选择视频' : '选择视频文件' }}
          </el-button>
          <el-button
            type="info"
            icon="el-icon-folder-opened"
            @click="showVideoListDialog"
          >
            查看视频列表
          </el-button>
          <!-- 手动传输按钮已删除，避免与脚本执行重复 -->
        </div>

        <!-- 已选择的视频预览 -->
        <div v-if="selectedVideos.length > 0" class="selected-videos-preview">
          <h5>已选择的视频 ({{ selectedVideos.length }})</h5>
          <div class="video-preview-grid">
            <!-- 使用Element UI Card组件 -->
            <el-card
              v-for="video in selectedVideos.slice(0, 6)"
              :key="video.id"
              class="video-preview-item"
              :body-style="{ padding: '0px' }"
              shadow="hover"
            >
              <div class="video-preview-container">
                <div class="video-thumbnail">
                  <img
                    v-if="video.thumbnail_path"
                    :src="getThumbnailUrl(video.thumbnail_path)"
                    :alt="video.original_name"
                  />
                  <div v-else class="no-thumbnail">
                    <i class="el-icon-video-camera"></i>
                  </div>
                  <div v-if="video.video_duration > 0" class="duration-badge">
                    {{ formatDuration(video.video_duration) }}
                  </div>

                  <!-- 播放按钮 -->
                  <div class="play-overlay">
                    <el-button
                      type="primary"
                      icon="el-icon-video-play"
                      circle
                      size="small"
                      class="play-button"
                    ></el-button>
                  </div>
                </div>
              </div>

              <div class="video-info" style="padding: 8px;">
                <div class="video-name">
                  <el-tooltip :content="video.original_name" placement="top">
                    <span>{{ video.original_name }}</span>
                  </el-tooltip>
                </div>

                <div class="video-meta">
                  <el-tag size="mini" type="info">{{ formatFileSize(video.file_size) }}</el-tag>
                  <el-tag size="mini" type="success">{{ video.video_format.toUpperCase() }}</el-tag>
                </div>

                <div class="video-actions" style="margin-top: 8px;">
                  <el-button
                    size="mini"
                    type="info"
                    icon="el-icon-data-line"
                    @click="showVideoTransferInfo(video)"
                  >
                    传输信息
                  </el-button>
                </div>
              </div>
            </el-card>

            <!-- 更多视频提示卡片 -->
            <el-card
              v-if="selectedVideos.length > 6"
              class="more-videos-card"
              :body-style="{ padding: '20px' }"
              shadow="hover"
            >
              <div class="more-videos-content">
                <div class="more-count">
                  <el-tag type="primary" size="large">+{{ selectedVideos.length - 6 }}</el-tag>
                </div>
                <div class="more-text">更多视频</div>
                <el-button
                  size="mini"
                  type="text"
                  @click="showVideoSelectionDialog"
                >
                  查看全部
                </el-button>
              </div>
            </el-card>
          </div>

          <div class="selection-summary">
            <div class="summary-info">
              <el-tag type="primary" size="medium">
                <i class="el-icon-video-camera"></i>
                总计: {{ selectedVideos.length }} 个视频，{{ formatFileSize(totalSelectedSize) }}
              </el-tag>
            </div>
            <div class="summary-actions">
              <el-button-group>
                <el-button
                  size="small"
                  type="warning"
                  icon="el-icon-delete"
                  @click="clearVideoSelection"
                >
                  清空选择
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  icon="el-icon-view"
                  @click="showAllVideosTransferInfo"
                >
                  查看传输记录
                </el-button>
              </el-button-group>
            </div>
          </div>
        </div>
        <div v-else class="no-videos-selected">
          <i class="el-icon-video-camera"></i>
          <p>请先选择要发布的视频文件</p>
        </div>
      </div>

      <!-- 发布配置 -->
      <el-form :model="config" label-width="120px" class="config-form">
        <el-form-item label="小红书应用">
          <el-select
            v-model="config.selectedApp"
            placeholder="请选择要使用的小红书应用"
            @change="onAppSelectionChange"
            style="width: 100%"
          >
            <el-option
              v-for="app in xiaohongshuApps"
              :key="app.text"
              :label="app.text"
              :value="app.text"
            >
              <span style="float: left">{{ app.text }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ app.method === 'keyword' ? '关键词' : '正则' }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="视频标题模板">
          <el-input
            v-model="config.titleTemplate"
            placeholder="输入视频标题模板，支持变量 {index}, {filename}"
            @input="onInputChange"
          />
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            支持变量：{index} - 序号，{filename} - 文件名
          </div>
        </el-form-item>

        <el-form-item label="视频描述">
          <el-input
            type="textarea"
            v-model="config.videoDescription"
            placeholder="输入视频描述内容"
            :rows="3"
            @input="onInputChange"
          />
        </el-form-item>

        <el-form-item label="话题标签">
          <el-input
            v-model="config.hashtags"
            placeholder="输入话题标签，用逗号分隔，如：#美食,#生活,#分享"
            @input="onInputChange"
          />
        </el-form-item>

        <el-form-item label="发布设置">
          <el-checkbox-group v-model="config.publishOptions">
            <el-checkbox label="allowComment">允许评论</el-checkbox>
            <el-checkbox label="allowShare">允许分享</el-checkbox>
            <el-checkbox label="showLocation">显示位置</el-checkbox>
            <el-checkbox label="addToStory">添加到动态</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="操作间隔">
          <el-input-number
            v-model="config.operationDelay"
            :min="3"
            :max="30"
            placeholder="秒"
            @change="onInputChange"
          />
          <span style="margin-left: 10px; color: #909399;">每个操作步骤间隔时间（秒）</span>
        </el-form-item>

        <el-form-item label="失败重试">
          <el-input-number
            v-model="config.retryCount"
            :min="0"
            :max="5"
            @change="onInputChange"
          />
          <span style="margin-left: 10px; color: #909399;">发布失败时的重试次数</span>
        </el-form-item>

        <!-- 脚本控制按钮 -->
        <el-form-item label="脚本控制">
          <el-button
            type="danger"
            size="small"
            @click="stopScript"
            :disabled="!isScriptRunning"
            icon="el-icon-video-pause"
          >
            停止脚本
          </el-button>
          <span v-if="isScriptRunning" style="margin-left: 10px; color: #67C23A; font-size: 12px;">
            脚本正在执行中...
          </span>
          <span v-else-if="currentStatus === '正在停止...'" style="margin-left: 10px; color: #E6A23C; font-size: 12px;">
            正在停止脚本...
          </span>
          <span v-else-if="isScriptCompleted" style="margin-left: 10px; color: #409EFF; font-size: 12px;">
            脚本执行完成
          </span>
          <span v-else-if="currentStatus === '已停止'" style="margin-left: 10px; color: #F56C6C; font-size: 12px;">
            脚本已停止
          </span>
        </el-form-item>
      </el-form>

      <!-- 实时状态显示区域 -->
      <div class="realtime-status-section">
        <h4>📊 实时状态</h4>
        <div class="status-grid">
          <div class="status-item">
            <span class="status-label">已发布视频数：</span>
            <span class="status-value">{{ publishedVideoCount }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">总视频数：</span>
            <span class="status-value">{{ totalVideoCount }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">当前步骤：</span>
            <span class="status-value">{{ currentStep }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">当前状态：</span>
            <span class="status-value" :class="getStatusClass(currentStatus)">{{ currentStatus }}</span>
          </div>
          <div v-if="errorMessage" class="status-item error">
            <span class="status-label">错误信息：</span>
            <span class="status-value">{{ errorMessage }}</span>
          </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-section">
          <el-progress
            :percentage="getProgressPercentage()"
            :status="getProgressStatus()"
            :show-text="true"
          />
        </div>

        <!-- 视频传输进度 - 一直显示 -->
        <div class="transfer-progress-section">
          <h4>📤 视频传输进度</h4>
          <div class="transfer-info">
            <div class="transfer-item">
              <span class="transfer-label">当前视频：</span>
              <span class="transfer-value">{{ videoTransferProgress.currentVideoName || '准备中...' }}</span>
            </div>
            <div class="transfer-item">
              <span class="transfer-label">传输状态：</span>
              <span class="transfer-value">
                <el-tag
                  :type="videoTransferProgress.status === 'completed' ? 'success' :
                         videoTransferProgress.status === 'failed' ? 'danger' :
                         videoTransferProgress.status === 'transferring' ? 'warning' : 'info'"
                  size="mini"
                >
                  {{
                    videoTransferProgress.status === 'completed' ? '传输完成' :
                    videoTransferProgress.status === 'failed' ? '传输失败' :
                    videoTransferProgress.status === 'transferring' ? '传输中' : '准备中'
                  }}
                </el-tag>
                <!-- 调试信息 -->
                <span style="margin-left: 10px; font-size: 12px; color: #999;">
                  (状态值: {{ videoTransferProgress.status }})
                </span>
              </span>
            </div>
            <div class="transfer-item">
              <span class="transfer-label">设备ID：</span>
              <span class="transfer-value">{{ videoTransferProgress.deviceId || '未设置' }}</span>
            </div>
          </div>

          <!-- 下载进度条 -->
          <div v-if="downloadProgress.isDownloading" class="download-progress" style="margin-bottom: 20px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>📥 下载进度</span>
              <span>{{ downloadProgress.percentage }}%</span>
            </div>
            <el-progress
              :percentage="downloadProgress.percentage"
              :show-text="false"
              stroke-width="12"
              color="#67C23A"
            />
            <div class="progress-details">
              <span class="size-info">{{ downloadProgress.downloadedSize }} / {{ downloadProgress.totalSize }}</span>
              <span class="speed-info">{{ downloadProgress.speed }}</span>
              <span class="time-info">{{ downloadProgress.message }}</span>
            </div>
          </div>

          <!-- 当前视频传输进度 -->
          <div class="video-progress">
            <el-progress
              :percentage="videoTransferProgress.currentVideoProgress"
              :show-text="true"
              stroke-width="12"
              :status="videoTransferProgress.status === 'failed' ? 'exception' :
                      videoTransferProgress.status === 'completed' ? 'success' : null"
            />
            <div class="progress-details">
              <span class="size-info">
                {{ formatFileSize(videoTransferProgress.currentVideoSize) }} /
                {{ formatFileSize(videoTransferProgress.currentVideoTotalSize) }}
              </span>
              <span v-if="videoTransferProgress.transferSpeed > 0" class="speed-info">
                {{ formatFileSize(videoTransferProgress.transferSpeed) }}/s
              </span>
              <span v-if="videoTransferProgress.estimatedTime > 0" class="time-info">
                剩余 {{ formatTime(videoTransferProgress.estimatedTime) }}
              </span>
            </div>
          </div>

          <!-- 传输提示 -->
          <div class="transfer-tips">
            <el-alert
              v-if="videoTransferProgress.currentVideoProgress === 0 && videoTransferProgress.status === 'pending'"
              title="正在准备传输..."
              type="info"
              :closable="false"
              show-icon
            >
              <template slot="description">
                大文件传输可能需要较长时间，请耐心等待。如果长时间无进度，请检查网络连接。
              </template>
            </el-alert>

            <el-alert
              v-if="videoTransferProgress.status === 'failed'"
              title="传输失败"
              type="error"
              :closable="false"
              show-icon
            >
              <template slot="description">
                视频传输失败，请检查网络连接和设备状态后重试。
              </template>
            </el-alert>
          </div>
        </div>
      </div>

      <el-alert
        title="使用提醒"
        type="info"
        :closable="false"
        show-icon
        style="margin-top: 20px;"
      >
        <div>
          • 请确保小红书应用已安装并已登录账号<br>
          • 视频文件将自动传输到手机设备<br>
          • 建议选择高质量的视频内容以提高发布成功率<br>
          • 发布过程中请勿手动操作手机<br>
          • 大量发布时请注意平台限制，避免账号异常
        </div>
      </el-alert>
    </el-card>

    <!-- 视频列表弹窗 -->
    <VideoListDialog
      v-model="videoListDialogVisible"
      @videos-uploaded="handleVideosUploaded"
      @show-transfer-info="showVideoTransferInfo"
    />

    <!-- 视频选择弹窗 -->
    <VideoSelectionDialog
      v-model="videoSelectionDialogVisible"
      :pre-selected-videos="selectedVideos"
      @videos-selected="handleVideosSelected"
      @show-transfer-info="showVideoTransferInfo"
    />

    <!-- 视频传输信息弹出框 -->
    <el-dialog
      :title="transferInfoDialogTitle"
      :visible.sync="transferInfoDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentVideoTransferInfo">
        <!-- 单个视频传输信息 -->
        <div class="video-transfer-info">
          <div class="video-basic-info">
            <h4>📹 视频基本信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">文件名：</span>
                  <span class="info-value">{{ currentVideoTransferInfo.original_name }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">文件大小：</span>
                  <span class="info-value">{{ formatFileSize(currentVideoTransferInfo.file_size) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">视频时长：</span>
                  <span class="info-value">{{ formatDuration(currentVideoTransferInfo.video_duration) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">上传时间：</span>
                  <span class="info-value">{{ formatDateTime(currentVideoTransferInfo.created_at) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="transfer-statistics">
            <h4>📊 传输统计</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-number">{{ transferStatistics.totalTransfers }}</div>
                  <div class="stat-label">总传输次数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-number">{{ transferStatistics.uniqueDevices }}</div>
                  <div class="stat-label">传输设备数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-number">{{ transferStatistics.successRate }}%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="transfer-records">
            <h4>📋 传输记录</h4>
            <el-table
              :data="transferRecords"
              style="width: 100%"
              max-height="300"
            >
              <el-table-column prop="device_name" label="设备名称" width="120"></el-table-column>
              <el-table-column prop="device_id" label="设备ID" width="180"></el-table-column>
              <el-table-column prop="device_ip" label="IP地址" width="120"></el-table-column>
              <el-table-column label="传输类型" width="100">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.transfer_type === 'manual' ? 'primary' : 'success'" size="mini">
                    {{ scope.row.transfer_type === 'manual' ? '手动传输' : '脚本传输' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="传输状态" width="100">
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.status === 'completed' ? 'success' :
                           scope.row.status === 'failed' ? 'danger' : 'warning'"
                    size="mini"
                  >
                    {{ getTransferStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="transfer_progress" label="进度" width="80">
                <template slot-scope="scope">
                  {{ scope.row.transfer_progress || 0 }}%
                </template>
              </el-table-column>
              <el-table-column prop="transfer_speed" label="传输速度" width="120">
                <template slot-scope="scope">
                  <span v-if="scope.row.transfer_speed && scope.row.transfer_speed > 0">
                    {{ formatFileSize(scope.row.transfer_speed * 1024) }}/s
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="transfer_time" label="开始时间" width="160">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.transfer_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="completed_time" label="完成时间" width="160">
                <template slot-scope="scope">
                  <span v-if="scope.row.completed_time">
                    {{ formatDateTime(scope.row.completed_time) }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="error_message" label="备注" show-overflow-tooltip></el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <div v-else-if="allVideosTransferInfo">
        <!-- 所有视频传输信息汇总 -->
        <div class="all-videos-transfer-info">
          <div class="summary-statistics">
            <h4>📊 传输统计汇总</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-number">{{ allTransferStatistics.totalVideos }}</div>
                  <div class="stat-label">视频总数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-number">{{ allTransferStatistics.totalTransfers }}</div>
                  <div class="stat-label">总传输次数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-number">{{ allTransferStatistics.uniqueDevices }}</div>
                  <div class="stat-label">传输设备数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-number">{{ allTransferStatistics.successRate }}%</div>
                  <div class="stat-label">平均成功率</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="videos-transfer-list">
            <h4>📋 视频传输详情</h4>
            <el-table
              :data="allVideosTransferInfo"
              style="width: 100%"
              max-height="400"
            >
              <el-table-column prop="original_name" label="视频名称" width="200" show-overflow-tooltip></el-table-column>
              <el-table-column prop="file_size" label="文件大小" width="100">
                <template slot-scope="scope">
                  {{ formatFileSize(scope.row.file_size) }}
                </template>
              </el-table-column>
              <el-table-column prop="transfer_count" label="传输次数" width="100"></el-table-column>
              <el-table-column prop="device_count" label="设备数" width="80"></el-table-column>
              <el-table-column prop="success_rate" label="成功率" width="80">
                <template slot-scope="scope">
                  {{ scope.row.success_rate }}%
                </template>
              </el-table-column>
              <el-table-column prop="last_transfer_time" label="最后传输时间" width="180">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.last_transfer_time) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="showVideoTransferInfo(scope.row)"
                  >
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="transferInfoDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 传输记录对话框 -->
    <el-dialog
      title="视频传输记录"
      :visible.sync="transferRecordsDialogVisible"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="transfer-records-content">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <el-form :inline="true" size="small">
            <el-form-item label="视频文件名:">
              <el-input
                v-model="transferRecordsFilter.videoFilename"
                placeholder="输入视频文件名"
                style="width: 200px;"
                @input="loadTransferRecords"
              />
            </el-form-item>
            <el-form-item label="设备ID:">
              <el-input
                v-model="transferRecordsFilter.deviceId"
                placeholder="输入设备ID"
                style="width: 200px;"
                @input="loadTransferRecords"
              />
            </el-form-item>
            <el-form-item label="状态:">
              <el-select
                v-model="transferRecordsFilter.status"
                placeholder="选择状态"
                style="width: 150px;"
                @change="loadTransferRecords"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="等待中" value="pending"></el-option>
                <el-option label="传输中" value="in_progress"></el-option>
                <el-option label="已完成" value="completed"></el-option>
                <el-option label="失败" value="failed"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadTransferRecords" icon="el-icon-refresh">刷新</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 传输记录表格 -->
        <el-table
          :data="transferRecordsList"
          v-loading="transferRecordsLoading"
          stripe
          border
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="id" label="记录ID" width="80" />
          <el-table-column prop="video_id" label="视频ID" width="80" />
          <el-table-column prop="video_filename" label="视频文件名" width="200" show-overflow-tooltip />
          <el-table-column prop="device_id" label="设备ID" width="180" />
          <el-table-column prop="device_name" label="设备名称" width="120" />
          <el-table-column prop="device_ip" label="设备IP" width="120" />
          <el-table-column label="传输类型" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.transfer_type === 'manual' ? 'primary' : 'success'" size="mini">
                {{ scope.row.transfer_type === 'manual' ? '手动' : '脚本' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.status === 'completed' ? 'success' :
                       scope.row.status === 'failed' ? 'danger' :
                       scope.row.status === 'in_progress' ? 'warning' : 'info'"
                size="mini"
              >
                {{
                  scope.row.status === 'completed' ? '已完成' :
                  scope.row.status === 'failed' ? '失败' :
                  scope.row.status === 'in_progress' ? '传输中' : '等待中'
                }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="transfer_progress" label="进度" width="80">
            <template slot-scope="scope">
              {{ scope.row.transfer_progress || 0 }}%
            </template>
          </el-table-column>
          <el-table-column prop="file_size" label="文件大小" width="100">
            <template slot-scope="scope">
              {{ formatFileSize(scope.row.file_size) }}
            </template>
          </el-table-column>
          <el-table-column prop="transfer_speed" label="传输速度" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.transfer_speed && scope.row.transfer_speed > 0">
                {{ formatFileSize(scope.row.transfer_speed * 1024) }}/s
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="transfer_time" label="开始时间" width="160">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.transfer_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="completed_time" label="完成时间" width="160">
            <template slot-scope="scope">
              <span v-if="scope.row.completed_time">
                {{ formatDateTime(scope.row.completed_time) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="task_id" label="任务ID" width="120" show-overflow-tooltip />
          <el-table-column prop="error_message" label="错误信息" min-width="200">
            <template slot-scope="scope">
              <span v-if="scope.row.error_message" style="color: #F56C6C;">
                {{ scope.row.error_message }}
              </span>
              <span v-else style="color: #67C23A;">正常</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section" style="margin-top: 20px; text-align: center;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="transferRecordsPagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="transferRecordsPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="transferRecordsPagination.total"
          />
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="transferRecordsDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import io from 'socket.io-client'
import xiaohongshuAppSelector from '@/mixins/xiaohongshuAppSelector'
import VideoListDialog from './VideoListDialog.vue'
import VideoSelectionDialog from './VideoSelectionDialog.vue'

export default {
  name: 'VideoPublishConfig',
  components: {
    VideoListDialog,
    VideoSelectionDialog
  },
  mixins: [xiaohongshuAppSelector],
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    deviceId: {
      type: String,
      default: ''
    },
    selectedDevices: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      config: {
        selectedApp: '',
        titleTemplate: '{filename}',
        videoDescription: '',
        hashtags: '',
        publishOptions: ['allowComment', 'allowShare'],
        operationDelay: 5,
        retryCount: 2
      },
      selectedVideos: [],
      videoListDialogVisible: false,
      videoSelectionDialogVisible: false,
      
      // 脚本执行状态
      isScriptRunning: false,
      isScriptCompleted: false,
      currentLogId: null,
      currentTaskId: null,
      
      // 实时状态变量
      publishedVideoCount: 0,
      totalVideoCount: 0,
      currentStep: '等待开始',
      currentStatus: '等待开始',
      errorMessage: '',

      // Socket连接
      socket: null,

      // 传输信息弹出框
      transferInfoDialogVisible: false,
      transferInfoDialogTitle: '',
      currentVideoTransferInfo: null,
      allVideosTransferInfo: null,
      transferRecords: [],
      transferStatistics: {
        totalTransfers: 0,
        uniqueDevices: 0,
        successRate: 0
      },

      // 视频传输进度
      videoTransferProgress: {
        isTransferring: false,
        currentVideoIndex: 0,
        totalVideos: 0,
        currentVideoName: '',
        currentVideoProgress: 0,
        currentVideoSize: 0,
        currentVideoTotalSize: 0,
        transferSpeed: 0,
        estimatedTime: 0,
        taskId: '',
        deviceId: '',
        status: 'pending'
      },
      allTransferStatistics: {
        totalVideos: 0,
        totalTransfers: 0,
        uniqueDevices: 0,
        successRate: 0
      },

      // 传输记录对话框
      transferRecordsDialogVisible: false,
      transferRecordsList: [],
      transferRecordsLoading: false,
      transferRecordsFilter: {
        videoFilename: '',
        deviceId: '',
        status: ''
      },
      transferRecordsPagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },

      // 下载进度数据
      downloadProgress: {
        isDownloading: false,
        percentage: 0,
        downloadedSize: '0MB',
        totalSize: '0MB',
        speed: '0MB/s',
        message: '准备下载...'
      },

      // 下载开始时间（用于计算速度）
      downloadStartTime: null
    }
  },
  computed: {
    canExecute() {
      return this.selectedVideos.length > 0 &&
             this.config.titleTemplate.trim() &&
             this.config.selectedApp
    },
    totalSelectedSize() {
      return this.selectedVideos.reduce((total, video) => total + video.file_size, 0)
    }
  },
  watch: {
    // 监听deviceId变化，重新恢复状态
    deviceId: {
      handler(newDeviceId, oldDeviceId) {
        console.log('📋 [VideoPublishConfig] 设备ID变化:', { oldDeviceId, newDeviceId })
        if (newDeviceId && newDeviceId !== oldDeviceId && this._isMounted) {
          console.log('🔄 [VideoPublishConfig] 设备ID已更新，重新恢复状态')
          // 只恢复进度信息，不重置其他状态
          this.restoreProgressInfo()
        }
      },
      immediate: false
    }
  },
  mounted() {
    console.log('🔄 [VideoPublishConfig] 组件已挂载，开始初始化')
    console.log('📋 [VideoPublishConfig] 当前设备ID:', this.deviceId)
    console.log('📋 [VideoPublishConfig] 传入的配置:', this.value)
    console.log('📊 [VideoPublishConfig] 初始状态:', {
      isScriptRunning: this.isScriptRunning,
      currentStatus: this.currentStatus,
      downloadProgress: this.downloadProgress,
      videoTransferProgress: this.videoTransferProgress
    })

    // 先初始化基础功能
    this.initSocketConnection()
    this.initTaskEventListener()

    // 使用nextTick确保DOM完全渲染后再恢复状态
    this.$nextTick(() => {
      // 先恢复状态，再初始化配置，避免覆盖恢复的配置
      this.restoreExecutionState()
      this.initConfig()

      // 再次使用nextTick确保状态恢复完成
      this.$nextTick(() => {
        console.log('📊 [VideoPublishConfig] 最终状态:', {
          isScriptRunning: this.isScriptRunning,
          currentStatus: this.currentStatus,
          downloadProgress: this.downloadProgress,
          videoTransferProgress: this.videoTransferProgress
        })
        console.log('✅ [VideoPublishConfig] 组件初始化完成')
      })
    })
  },
  beforeDestroy() {
    console.log('🔧 [VideoPublishConfig] 组件即将销毁，执行清理操作')

    // 不再断开socket连接，让WebSocketManager统一管理
    console.log('🔧 [VideoPublishConfig] 保持WebSocket连接，仅清理事件监听')

    // 移除任务事件监听
    this.$root.$off('xiaohongshu-task-started')
    this.$root.$off('xiaohongshu-task-stopped')
    this.$root.$off('xiaohongshu-script-completed')
    this.$root.$off('xiaohongshu-video-publish-select-video')
    this.$root.$off('xiaohongshu-video-publish-clear-video')
    this.$root.$off('device-offline')

    console.log('🔧 [VideoPublishConfig] 组件清理完成')
  },
  methods: {
    initConfig() {
      console.log('📋 [配置初始化] 开始初始化配置，设备ID:', this.deviceId)

      // 只有在没有恢复配置的情况下才使用传入的value
      if (this.value && Object.keys(this.value).length > 0) {
        // 检查是否已经有恢复的配置（使用设备特定的键名）
        const configKey = this.deviceId ? `videoPublish_config_${this.deviceId}` : 'videoPublish_config'
        const hasRestoredConfig = localStorage.getItem(configKey)

        console.log('📋 [配置初始化] 检查配置键:', configKey)
        console.log('📋 [配置初始化] 是否有已恢复的配置:', !!hasRestoredConfig)

        if (!hasRestoredConfig) {
          this.config = { ...this.config, ...this.value }
          console.log('📋 [配置初始化] 使用传入的配置参数:', this.value)
        } else {
          console.log('📋 [配置初始化] 跳过传入配置，使用已恢复的配置')
        }
      } else {
        console.log('📋 [配置初始化] 没有传入的配置参数')
      }

      this.emitConfigUpdate()
    },

    showVideoListDialog() {
      this.videoListDialogVisible = true
    },

    showVideoSelectionDialog() {
      // 检查是否已经选择了视频
      if (this.selectedVideos.length >= 1) {
        this.$message.warning('每个设备只能选择一个视频，请先清空当前选择')
        return
      }
      this.videoSelectionDialogVisible = true
    },

    handleVideosUploaded(data) {
      this.$message.success(`成功上传 ${data.uploadCount} 个视频文件`)
      if (data.duplicateCount > 0) {
        this.$message.warning(`其中 ${data.duplicateCount} 个重复文件已跳过`)
      }
    },

    handleVideosSelected(videos) {
      // 限制每个设备只能选择一个视频
      if (videos.length > 1) {
        this.$message.warning('每个设备只能选择一个视频进行发布')
        this.selectedVideos = [videos[0]] // 只保留第一个视频
        this.totalVideoCount = 1
      } else {
        this.selectedVideos = videos
        this.totalVideoCount = videos.length
      }

      // 保存视频选择
      this.saveVideosToStorage()
      this.emitConfigUpdate()

      if (this.selectedVideos.length > 0) {
        this.$message.success(`已选择视频文件: ${this.selectedVideos[0].original_name}`)
      }
    },

    clearVideoSelection() {
      this.selectedVideos = []
      this.totalVideoCount = 0
      this.saveVideosToStorage()
      this.emitConfigUpdate()
    },

    // 保存视频选择到本地存储
    saveVideosToStorage() {
      try {
        if (this.deviceId) {
          const videosKey = `videoPublish_selectedVideos_${this.deviceId}`
          localStorage.setItem(videosKey, JSON.stringify(this.selectedVideos))
          console.log(`💾 [视频保存] 设备 ${this.deviceId} 的视频选择已保存`)
        } else {
          localStorage.setItem('videoPublish_selectedVideos', JSON.stringify(this.selectedVideos))
          console.log('💾 [视频保存] 通用视频选择已保存')
        }
      } catch (error) {
        console.error('❌ [视频保存] 保存视频选择失败:', error)
      }
    },

    // 手动传输视频方法已删除，避免与脚本执行重复

    // 显示传输结果
    showTransferResults(data) {
      const resultMessage = `
传输任务ID: ${data.transferTaskId}
视频数量: ${data.videoCount}
目标设备: ${data.deviceCount}

传输的视频:
${data.videos.map(v => `• ${v.name} (${this.formatFileSize(v.size)})`).join('\n')}

设备状态:
${data.results.map(r => `• ${r.deviceId}: ${r.status === 'success' ? '✅' : '❌'} ${r.message}`).join('\n')}
      `

      this.$alert(resultMessage, '视频传输结果', {
        confirmButtonText: '确定',
        type: 'info'
      })
    },

    getThumbnailUrl(thumbnailPath) {
      if (!thumbnailPath) return ''
      const { getApiBaseUrl } = require('@/utils/serverConfig')
      const baseUrl = process.env.VUE_APP_API_BASE_URL || getApiBaseUrl()
      return `${baseUrl}${thumbnailPath}`
    },

    onInputChange() {
      this.emitConfigUpdate()
      // 保存配置参数
      this.saveConfigToStorage()
    },

    // 保存配置到本地存储
    saveConfigToStorage() {
      try {
        if (this.deviceId) {
          const configKey = `videoPublish_config_${this.deviceId}`
          localStorage.setItem(configKey, JSON.stringify(this.config))
          console.log(`💾 [配置保存] 设备 ${this.deviceId} 的配置已保存`)
        } else {
          localStorage.setItem('videoPublish_config', JSON.stringify(this.config))
          console.log('💾 [配置保存] 通用配置已保存')
        }
      } catch (error) {
        console.error('❌ [配置保存] 保存配置失败:', error)
      }
    },

    onAppSelectionChange() {
      this.emitConfigUpdate()
    },

    emitConfigUpdate() {
      const configData = {
        ...this.config,
        selectedVideoIds: this.selectedVideos.map(v => v.id),
        selectedVideos: this.selectedVideos
      }
      this.$emit('input', configData)
      this.$emit('update', configData)
    },

    initSocketConnection() {
      // 只使用WebSocket传输，不使用长轮询
      this.socket = io({
        transports: ['websocket'], // 只使用WebSocket，不降级到polling
        upgrade: false // 禁用传输升级
      })

      this.socket.on('connect', () => {
        console.log('✅ [VideoPublishConfig] Socket连接成功')

        // 发送用户认证信息，确保能接收到用户隔离的广播消息
        const user = this.$store.getters['auth/user']
        const isAuthenticated = this.$store.getters['auth/isAuthenticated']

        if (isAuthenticated && user) {
          console.log('🔍 [VideoPublishConfig] 发送认证用户连接:', { userId: user.id, username: user.username })
          this.socket.emit('web_client_connect', {
            userId: user.id,
            username: user.username,
            clientType: 'video_publish_config'
          })
          console.log('✅ [VideoPublishConfig] 已注册为认证Web客户端:', user.username)
        } else {
          // 未登录状态下注册为匿名客户端
          const anonymousUserId = 'anonymous_video_publish_' + Date.now()
          console.log('🔍 [VideoPublishConfig] 发送匿名用户连接:', { userId: anonymousUserId })
          this.socket.emit('web_client_connect', {
            userId: anonymousUserId,
            username: 'anonymous',
            clientType: 'anonymous_video_publish'
          })
          console.log('✅ [VideoPublishConfig] 已注册为匿名Web客户端')
        }
      })

      this.socket.on('disconnect', () => {
        console.log('❌ [VideoPublishConfig] Socket连接断开')
      })

      // 监听实时状态更新
      this.socket.on('xiaohongshu_realtime_status', (data) => {
        console.log('🎯 [VideoPublishConfig] 收到实时状态更新:', data)
        this.handleRealtimeStatus(data)
      })

      // 保留原有的进度和完成事件监听
      this.socket.on('xiaohongshu_video_publish_progress', (data) => {
        if (data.deviceId === this.deviceId) {
          this.handleProgressUpdate(data)
        }
      })

      this.socket.on('xiaohongshu_video_publish_completed', (data) => {
        if (data.deviceId === this.deviceId) {
          this.handleExecutionCompleted(data)
        }
      })

      // 监听视频传输进度
      this.socket.on('video_transfer_progress', (data) => {
        console.log('📊 [VideoPublishConfig] 收到视频传输进度:', data)
        this.handleTransferProgress(data)
      })
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('🔄 [VideoPublishConfig] 收到实时状态数据:', data)
      console.log('📋 [VideoPublishConfig] 当前组件taskId:', this.currentTaskId)
      console.log('📋 [VideoPublishConfig] 数据中的taskId:', data.taskId)

      // 检查是否是当前任务的状态更新，如果currentTaskId为空，则从数据中设置
      if (!this.currentTaskId && data.taskId) {
        console.log('🔧 [VideoPublishConfig] currentTaskId为空，从实时状态数据中设置:', data.taskId)
        this.currentTaskId = data.taskId
        this.currentLogId = data.taskId
        this.isScriptRunning = true
        this.isScriptCompleted = false
        this.currentStatus = '脚本正在执行中...'
        this.saveExecutionState()
      }

      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        console.log('✅ [VideoPublishConfig] taskId匹配，更新实时状态')

        // 更新实时状态变量
        if (data.publishedVideoCount !== undefined) {
          this.publishedVideoCount = data.publishedVideoCount
        }
        if (data.totalVideoCount !== undefined) {
          this.totalVideoCount = data.totalVideoCount
        }
        if (data.currentStep) {
          this.currentStep = data.currentStep
        }
        if (data.currentStatus) {
          this.currentStatus = data.currentStatus
        }
        if (data.errorMessage) {
          this.errorMessage = data.errorMessage
        }

        // 解析下载进度信息
        this.parseDownloadProgress(data)

        // 检查是否完成（只有脚本真正执行完成才设置完成状态）
        if (data.currentStatus) {
          // 使用最严格的判断条件：只有明确的finished状态才认为脚本完成
          if (data.currentStatus === 'finished') {
            this.isScriptRunning = false
            this.isScriptCompleted = true
            console.log('✅ [VideoPublishConfig] 脚本执行完成 - currentStatus: finished')
          } else if (data.currentStatus === 'error') {
            this.isScriptRunning = false
            this.isScriptCompleted = true
            console.log('❌ [VideoPublishConfig] 脚本执行出错 - currentStatus: error')
          } else {
            // 所有其他状态（包括completed）都不设置脚本完成状态
            console.log('🔍 [VideoPublishConfig] 中间状态，不设置完成 - currentStatus:', data.currentStatus, 'currentStep:', data.currentStep)
          }
        }

        console.log('📊 [VideoPublishConfig] 状态已更新:', {
          publishedVideoCount: this.publishedVideoCount,
          totalVideoCount: this.totalVideoCount,
          currentStep: this.currentStep,
          currentStatus: this.currentStatus,
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted
        })

        // 自动保存状态更新
        this.saveExecutionState()
      } else {
        console.log('⚠️ [VideoPublishConfig] taskId不匹配，忽略状态更新')
      }
    },

    // 初始化任务事件监听
    initTaskEventListener() {
      console.log('🎯 [VideoPublishConfig] 初始化任务事件监听，设备ID:', this.deviceId)

      // 监听任务开始事件
      this.$root.$on('xiaohongshu-task-started', (data) => {
        console.log('🚀 [VideoPublishConfig] 收到任务开始事件:', data)
        console.log('📋 [VideoPublishConfig] 当前设备ID:', this.deviceId)
        console.log('📋 [VideoPublishConfig] 事件设备ID:', data.deviceId)
        console.log('📋 [VideoPublishConfig] 事件功能类型:', data.functionType)

        // 检查是否是当前设备的视频发布任务
        if (data.deviceId === this.deviceId && data.functionType === 'videoPublish') {
          console.log('✅ [VideoPublishConfig] 设备ID和功能类型匹配，处理任务开始事件')
          this.handleTaskStarted(data)
        } else {
          console.log('⚠️ [VideoPublishConfig] 设备ID不匹配或非视频发布任务，忽略事件')
          console.log('📋 [VideoPublishConfig] 匹配条件:', {
            deviceIdMatch: data.deviceId === this.deviceId,
            functionTypeMatch: data.functionType === 'videoPublish',
            expectedDeviceId: this.deviceId,
            actualDeviceId: data.deviceId,
            expectedFunctionType: 'videoPublish',
            actualFunctionType: data.functionType
          })
        }
      })

      // 监听视频分配选择事件
      this.$root.$on('xiaohongshu-video-publish-select-video', (data) => {
        console.log('🎬 [VideoPublishConfig] 收到视频选择事件:', data)
        console.log('📋 [VideoPublishConfig] 当前设备ID:', this.deviceId)
        console.log('📋 [VideoPublishConfig] 事件设备ID:', data.deviceId)

        // 检查是否是当前设备的视频选择
        if (data.deviceId === this.deviceId) {
          console.log('✅ [VideoPublishConfig] 设备ID匹配，处理视频选择事件')
          this.handleVideoAssignmentSelect(data)
        } else {
          console.log('⚠️ [VideoPublishConfig] 设备ID不匹配，忽略视频选择事件')
        }
      })

      // 监听视频清除事件
      this.$root.$on('xiaohongshu-video-publish-clear-video', (data) => {
        console.log('🗑️ [VideoPublishConfig] 收到视频清除事件:', data)
        console.log('📋 [VideoPublishConfig] 当前设备ID:', this.deviceId)
        console.log('📋 [VideoPublishConfig] 事件设备ID:', data.deviceId)

        // 检查是否是当前设备的视频清除
        if (data.deviceId === this.deviceId) {
          console.log('✅ [VideoPublishConfig] 设备ID匹配，清除视频选择')
          this.clearVideoSelection()
        } else {
          console.log('⚠️ [VideoPublishConfig] 设备ID不匹配，忽略视频清除事件')
        }
      })

      // 监听任务停止事件
      this.$root.$on('xiaohongshu-task-stopped', (data) => {
        console.log('[VideoPublishConfig] 收到任务停止事件:', data)
        const functionType = typeof data === 'string' ? data : data.functionType
        const reason = data.reason || 'manual'

        // 处理批量停止或单设备停止
        const shouldStop = functionType === 'videoPublish' && (
          reason === 'batch_stop' || // 批量停止时停止所有设备
          !this.deviceId || // 没有设备ID时停止
          data.deviceId === this.deviceId // 设备ID匹配时停止
        )

        if (shouldStop) {
          console.log(`[VideoPublishConfig] 视频发布任务停止，原因: ${reason}`)
          this.handleTaskStopped(data)

          // 如果是批量停止，显示提示信息
          if (reason === 'batch_stop') {
            this.$message.info('视频发布功能已被批量停止')
          }
        }
      })

      // 监听脚本完成事件
      this.$root.$on('xiaohongshu-script-completed', (data) => {
        console.log('[VideoPublishConfig] 收到脚本完成事件:', data)
        if (data.functionType === 'videoPublish' && (!this.deviceId || data.deviceId === this.deviceId)) {
          console.log('[VideoPublishConfig] 视频发布脚本完成，状态:', data.status)
          this.handleScriptCompleted(data)
        }
      })

      // 监听设备离线事件
      this.$root.$on('device-offline', (data) => {
        if (data.deviceId === this.deviceId) {
          console.log('[VideoPublishConfig] 当前设备离线，重置状态')
          this.handleDeviceOffline()
        }
      })
    },

    // 处理视频分配选择事件
    handleVideoAssignmentSelect(data) {
      try {
        console.log('🎯 [VideoPublishConfig] 处理视频分配选择:', data)
        console.log('📋 [VideoPublishConfig] 当前已选视频:', this.selectedVideos)

        const { video, videoInfo } = data

        // 直接设置新分配的视频（清除操作已在上级处理）
        this.selectedVideos = [video]
        this.totalVideoCount = 1

        console.log('✅ [VideoPublishConfig] 已设置新分配的视频:', video.original_name)

        // 保存到本地存储
        this.saveVideosToStorage()

        // 发射配置更新事件
        this.emitConfigUpdate()

        // 强制更新UI
        this.$forceUpdate()

        console.log('✅ [VideoPublishConfig] 视频分配选择完成:', video.original_name)
        this.$message.success(`已为设备分配视频: ${video.original_name}`)

      } catch (error) {
        console.error('❌ [VideoPublishConfig] 处理视频分配选择失败:', error)
        this.$message.error('设置分配视频失败: ' + error.message)
      }
    },

    // 处理任务开始事件
    handleTaskStarted(data) {
      // 避免重复处理同一个任务
      if (this.currentTaskId === data.taskId) {
        console.log(`[VideoPublishConfig] 跳过重复的任务开始事件: ${data.taskId}`)
        return
      }

      console.log(`[VideoPublishConfig] 处理任务开始事件: ${data.taskId}`)

      // 设置任务ID和执行状态
      this.currentTaskId = data.taskId
      this.currentLogId = data.logId || data.taskId
      this.isScriptRunning = true
      this.isScriptCompleted = false
      this.currentStatus = '任务已下发，等待设备获取脚本...'
      this.currentStep = '等待脚本获取'
      this.publishedVideoCount = 0
      this.errorMessage = ''

      // 保存执行状态
      this.saveExecutionState()

      // 只在第一次处理时显示成功消息
      this.$message.success(`设备 ${this.deviceId} 视频发布任务已下发`)
    },

    // 处理任务停止事件
    handleTaskStopped(data) {
      console.log('[VideoPublishConfig] 处理任务停止事件:', data)

      // 重置执行状态
      this.isScriptRunning = false
      this.isScriptCompleted = false
      this.currentStatus = '已停止'
      this.currentStep = '任务已停止'
      this.errorMessage = ''

      // 更新Vuex状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'videoPublish',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          selectedDevices: [], // 清空选中设备列表
          config: this.config
        }
      })

      // 🔥 关键修复：通知主页面更新批量停止按钮状态
      // 直接调用主页面的handleScriptCompleted方法
      if (this.$parent && this.$parent.handleScriptCompleted) {
        this.$parent.handleScriptCompleted({
          functionType: 'videoPublish',
          deviceId: this.deviceId,
          taskId: data.taskId || `task_${this.deviceId}`,
          status: 'stopped',
          message: '脚本已停止',
          timestamp: new Date().toISOString()
        })
        console.log('✅ [VideoPublishConfig] 已直接调用主页面的handleScriptCompleted方法')
      } else {
        console.warn('⚠️ [VideoPublishConfig] 无法找到主页面的handleScriptCompleted方法')
      }

      // 保存状态
      this.saveExecutionState()

      console.log('[VideoPublishConfig] 任务停止处理完成，状态已更新为已停止，已通知主页面更新批量停止按钮')
    },

    // 处理脚本完成事件
    handleScriptCompleted(data) {
      console.log('[VideoPublishConfig] 处理脚本完成事件:', data)

      if (data.status === 'stopped') {
        // 手动停止
        console.log('[VideoPublishConfig] 视频发布脚本被手动停止')
        this.isScriptRunning = false
        this.isScriptCompleted = false
        this.currentStatus = '已停止'
        this.currentStep = '脚本已停止'
        this.errorMessage = ''
      } else if (data.status === 'success') {
        // 执行成功
        console.log('[VideoPublishConfig] 视频发布脚本执行成功')
        this.isScriptRunning = false
        this.isScriptCompleted = true
        this.currentStatus = '执行完成'
        this.currentStep = '视频发布完成'
        this.errorMessage = ''
      } else if (data.status === 'error' || data.status === 'failed') {
        // 执行失败
        console.log('[VideoPublishConfig] 视频发布脚本执行失败')
        this.isScriptRunning = false
        this.isScriptCompleted = true
        this.currentStatus = '执行失败'
        this.currentStep = '脚本出错'
        this.errorMessage = data.message || '脚本执行失败'
      }

      // 更新Vuex状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'videoPublish',
        stateData: {
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted,
          config: this.config
        }
      })

      // 保存状态
      this.saveExecutionState()

      console.log('[VideoPublishConfig] 脚本完成处理完成，最终状态:', {
        isScriptRunning: this.isScriptRunning,
        currentStatus: this.currentStatus,
        currentStep: this.currentStep
      })
    },

    handleProgressUpdate(data) {
      this.publishedVideoCount = data.publishedCount || 0
      this.currentStep = data.currentStep || '执行中'
      this.currentStatus = `发布视频中 (${this.publishedVideoCount}/${this.totalVideoCount})`

      if (data.errorMessage) {
        this.errorMessage = data.errorMessage
      }
    },

    handleExecutionCompleted(data) {
      this.isScriptRunning = false
      this.isScriptCompleted = true
      this.publishedVideoCount = data.publishedCount || 0
      this.currentStatus = data.success ? '发布完成' : '发布失败'
      
      if (data.errorMessage) {
        this.errorMessage = data.errorMessage
      }

      // 1分钟后重置状态
      setTimeout(() => {
        this.resetExecutionState()
      }, 60000)
    },

    // startScript方法已删除，避免重复执行

    async transferVideosToDevice() {
      try {
        console.log('📤 [视频传输] 开始传输视频到设备:', this.deviceId)
        console.log('📤 [视频传输] 传输的视频:', this.selectedVideos.map(v => ({ id: v.id, name: v.original_name })))

        // 初始化传输进度
        this.initTransferProgress()

        // 重置下载进度
        this.downloadProgress = {
          isDownloading: false,
          percentage: 0,
          downloadedSize: '0MB',
          totalSize: '0MB',
          speed: '0MB/s',
          message: '准备下载...'
        }
        this.downloadStartTime = null

        // 设置传输任务信息（使用脚本执行的任务ID）
        this.videoTransferProgress.taskId = this.currentTaskId || `video_transfer_${Date.now()}`
        this.videoTransferProgress.deviceId = this.deviceId

        const token = this.$store.getters['auth/token']

        // 开始监听传输进度
        this.startTransferProgressMonitoring()

        const response = await this.$http.post('/api/xiaohongshu/transfer-videos', {
          deviceIds: [this.deviceId],
          selectedVideoIds: this.selectedVideos.map(v => v.id),
          selectedVideos: this.selectedVideos
        }, {
          headers: {
            'Content-Type': 'application/json',
            ...(token ? { 'Authorization': `Bearer ${token}` } : {})
          }
        })

        console.log('📤 [视频传输] 传输响应:', response.data)

        // 停止传输进度监控
        this.stopTransferProgressMonitoring()

        // 传输成功，但不需要在这里记录到数据库
        // 因为Auto.js脚本会通过reportTransferStatus自动上报状态
        if (response.data.success) {
          console.log('✅ [视频传输] 传输成功，脚本将自动上报传输状态')
        }

        return response.data
      } catch (error) {
        console.error('❌ [视频传输] 传输视频失败:', error)

        // 停止传输进度监控
        this.stopTransferProgressMonitoring()

        // 传输失败，脚本会自动上报失败状态
        console.log('❌ [视频传输] 传输失败，脚本将自动上报失败状态')

        throw error
      }
    },

    // 记录视频传输到数据库
    async recordVideoTransfers(status = 'completed', errorMessage = null) {
      try {
        console.log('📝 [视频传输记录] 开始记录传输记录')

        // 获取认证token
        const token = localStorage.getItem('token')

        for (const video of this.selectedVideos) {
          console.log(`📝 [视频传输记录] 记录视频 ${video.id} 传输到设备 ${this.deviceId}`)

          const response = await this.$http.post('/api/videos/transfer-record', {
            videoId: video.id,
            deviceId: this.deviceId,
            status: status,
            errorMessage: errorMessage
          }, {
            headers: {
              'Content-Type': 'application/json',
              ...(token ? { 'Authorization': `Bearer ${token}` } : {})
            }
          })

          if (response.data.success) {
            console.log(`✅ [视频传输记录] 视频 ${video.id} 传输记录保存成功`)
          } else {
            console.error(`❌ [视频传输记录] 视频 ${video.id} 传输记录保存失败:`, response.data.message)
          }
        }

        console.log('✅ [视频传输记录] 所有传输记录保存完成')
      } catch (error) {
        console.error('❌ [视频传输记录] 记录传输失败:', error)
      }
    },

    // 初始化传输进度
    initTransferProgress() {
      this.videoTransferProgress = {
        isTransferring: true,
        currentVideoIndex: 0,
        totalVideos: this.selectedVideos.length,
        currentVideoName: this.selectedVideos[0]?.original_name || '准备中...',
        currentVideoProgress: 0,
        currentVideoSize: 0,
        currentVideoTotalSize: this.selectedVideos[0]?.file_size || 0,
        transferSpeed: 0,
        estimatedTime: 0,
        status: 'pending', // 添加初始状态
        taskId: '', // 添加任务ID字段
        deviceId: '' // 添加设备ID字段
      }
      console.log('📊 [传输进度] 初始化传输进度:', this.videoTransferProgress)
    },

    // 开始传输进度监控
    startTransferProgressMonitoring() {
      console.log('📊 [传输进度] 开始监控传输进度')
      console.log('📊 [传输进度] 任务ID:', this.videoTransferProgress.taskId)
      console.log('📊 [传输进度] 设备ID:', this.videoTransferProgress.deviceId)

      // WebSocket监听器已在initSocketConnection中设置，这里不需要重复设置
      console.log('📊 [传输进度] WebSocket监听器已在初始化时设置')

      // 设置超时检查，如果长时间没有进度更新，显示提示
      this.transferTimeoutCheck = setTimeout(() => {
        if (this.videoTransferProgress.isTransferring && this.videoTransferProgress.currentVideoProgress === 0) {
          console.log('⚠️ [传输进度] 传输可能卡住，显示提示信息')
          this.$message.warning('视频传输可能需要较长时间，请耐心等待...')
        }
      }, 10000) // 10秒后检查
    },

    // 处理传输进度数据
    handleTransferProgress(data) {
      console.log('📊 [传输进度] 接收到进度数据:', data)
      console.log('📊 [传输进度] 当前组件deviceId:', this.deviceId)
      console.log('📊 [传输进度] 当前videoTransferProgress.deviceId:', this.videoTransferProgress.deviceId)

      // 基于设备ID匹配，移除isTransferring条件限制，允许接收所有相关设备的进度数据
      if (data.deviceId === this.deviceId) {
        console.log('📊 [传输进度] 设备ID匹配，开始更新进度')
        console.log('📊 [传输进度] 当前isTransferring状态:', this.videoTransferProgress.isTransferring)

        // 如果还没有开始传输监控，自动开始
        if (!this.videoTransferProgress.isTransferring) {
          console.log('📊 [传输进度] 自动开始传输监控')
          this.videoTransferProgress.isTransferring = true
        }

        // 使用Vue.set确保响应式更新
        this.$set(this.videoTransferProgress, 'deviceId', data.deviceId)
        this.$set(this.videoTransferProgress, 'currentVideoName', data.videoName)
        this.$set(this.videoTransferProgress, 'currentVideoProgress', parseInt(data.progress) || 0)
        this.$set(this.videoTransferProgress, 'currentVideoSize', parseInt(data.transferredBytes) || 0)
        this.$set(this.videoTransferProgress, 'currentVideoTotalSize', parseInt(data.totalBytes) || 0)
        this.$set(this.videoTransferProgress, 'transferSpeed', parseInt(data.transferSpeed) || 0)
        this.$set(this.videoTransferProgress, 'status', data.status)

        // 计算预估时间
        const remainingSize = this.videoTransferProgress.currentVideoTotalSize - this.videoTransferProgress.currentVideoSize
        this.videoTransferProgress.estimatedTime =
          this.videoTransferProgress.transferSpeed > 0 ? remainingSize / this.videoTransferProgress.transferSpeed : 0

        console.log('📊 [传输进度] 进度已更新:', {
          progress: data.progress + '%',
          speed: this.formatFileSize(data.transferSpeed) + '/s',
          status: data.status,
          currentVideoName: data.videoName,
          deviceId: data.deviceId
        })

        // 输出当前videoTransferProgress的完整状态
        console.log('📊 [传输进度] 当前videoTransferProgress状态:', JSON.stringify(this.videoTransferProgress, null, 2))

        // 强制触发Vue的响应式更新
        this.$forceUpdate()

        // 如果传输完成或失败，延迟停止监控以确保UI能显示最终状态
        if (data.status === 'completed' || data.status === 'failed') {
          console.log('📊 [传输进度] 传输结束，状态:', data.status)
          // 延长显示时间，确保用户能看到完成状态
          setTimeout(() => {
            this.stopTransferProgressMonitoring()
          }, 5000) // 5秒后停止显示
        }
      } else {
        console.log('📊 [传输进度] 设备ID不匹配，忽略进度数据')
        console.log('📊 [传输进度] 期望设备ID:', this.deviceId)
        console.log('📊 [传输进度] 接收到设备ID:', data.deviceId)
      }
    },

    // 停止传输进度监控
    stopTransferProgressMonitoring() {
      console.log('📊 [传输进度] 停止监控传输进度')

      // 清除超时检查
      if (this.transferTimeoutCheck) {
        clearTimeout(this.transferTimeoutCheck)
        this.transferTimeoutCheck = null
      }

      // 不移除WebSocket监听器，因为它是全局的，可能被其他传输使用
      console.log('📊 [传输进度] 保持WebSocket监听器，停止当前传输监控')

      // 重置传输状态，但保留最后的进度信息供用户查看
      this.videoTransferProgress.isTransferring = false
      console.log('📊 [传输进度] 传输监控已停止，最终状态:', {
        status: this.videoTransferProgress.status,
        progress: this.videoTransferProgress.currentVideoProgress + '%',
        videoName: this.videoTransferProgress.currentVideoName
      })
    },

    // 恢复执行状态
    restoreExecutionState() {
      try {
        console.log('🔄 [状态恢复] 检查设备执行状态，设备ID:', this.deviceId)

        if (!this.deviceId) {
          console.log('⚠️ [状态恢复] 设备ID为空，跳过状态恢复')
          return
        }

        // 从localStorage恢复执行状态
        const savedState = localStorage.getItem(`videoPublish_executionState_${this.deviceId}`)
        if (savedState) {
          const state = JSON.parse(savedState)
          console.log('📋 [状态恢复] 找到保存的执行状态:', state)

          // 检查状态是否仍然有效（不超过2小时）
          const stateAge = Date.now() - state.timestamp
          if (stateAge < 2 * 60 * 60 * 1000) { // 2小时内
            console.log('✅ [状态恢复] 执行状态仍然有效，恢复状态')

            // 恢复执行状态
            this.isScriptRunning = state.isScriptRunning || false
            this.isScriptCompleted = state.isScriptCompleted || false
            this.currentTaskId = state.currentTaskId || null
            this.currentLogId = state.currentLogId || null

            // 只有在脚本正在运行时才恢复执行状态信息，否则重置为初始状态
            if (this.isScriptRunning) {
              this.currentStatus = state.currentStatus || '等待开始'
              this.currentStep = state.currentStep || '等待开始'
              this.publishedVideoCount = state.publishedVideoCount || 0
              this.totalVideoCount = state.totalVideoCount || 0
              this.errorMessage = state.errorMessage || ''

              console.log('🔄 [状态恢复] 脚本正在执行中，恢复执行状态并检查状态')
              // 先检查执行日志，确定脚本是否真的还在执行
              this.checkExecutionLogStatus()
            } else {
              // 脚本没有运行，重置显示状态为初始值
              this.currentStatus = '等待开始'
              this.currentStep = '等待开始'
              this.publishedVideoCount = 0
              this.totalVideoCount = 0
              this.errorMessage = ''

              console.log('📊 [状态恢复] 脚本未运行，已重置显示状态为初始值')
            }

            console.log('📊 [状态恢复] 执行状态已恢复:', {
              isScriptRunning: this.isScriptRunning,
              currentStatus: this.currentStatus,
              currentTaskId: this.currentTaskId,
              currentLogId: this.currentLogId
            })
          } else {
            console.log('⏰ [状态恢复] 执行状态已过期，清除保存的状态')
            this.clearDeviceExecutionState()
          }
        } else {
          console.log('📋 [状态恢复] 没有找到保存的执行状态')
        }

        // 无论是否找到执行状态，都尝试恢复进度信息
        this.restoreProgressInfo()

        // 从localStorage恢复配置参数
        const savedConfig = localStorage.getItem(`videoPublish_config_${this.deviceId}`)
        if (savedConfig) {
          const config = JSON.parse(savedConfig)
          console.log('📋 [状态恢复] 找到保存的配置:', config)
          console.log('📋 [状态恢复] 恢复前的配置:', this.config)

          // 恢复配置参数
          this.config = { ...this.config, ...config }
          console.log('✅ [状态恢复] 配置参数已恢复:', this.config)
        } else {
          console.log('📋 [状态恢复] 没有找到保存的配置，键名:', `videoPublish_config_${this.deviceId}`)
        }

        // 从localStorage恢复选中的视频
        const savedVideos = localStorage.getItem(`videoPublish_selectedVideos_${this.deviceId}`)
        if (savedVideos) {
          const videos = JSON.parse(savedVideos)
          console.log('📋 [状态恢复] 找到保存的视频选择:', videos)
          console.log('📋 [状态恢复] 恢复前的视频选择:', this.selectedVideos)

          this.selectedVideos = videos
          this.totalVideoCount = videos.length
          console.log('✅ [状态恢复] 视频选择已恢复:', this.selectedVideos)
        } else {
          console.log('📋 [状态恢复] 没有找到保存的视频选择，键名:', `videoPublish_selectedVideos_${this.deviceId}`)
        }



        // 恢复配置后触发配置更新事件，确保UI正确显示
        this.$nextTick(() => {
          this.emitConfigUpdate()
          console.log('✅ [状态恢复] 配置更新事件已触发')
        })

      } catch (error) {
        console.error('❌ [状态恢复] 恢复状态失败:', error)
        localStorage.removeItem('videoPublish_executionState')
        localStorage.removeItem('videoPublish_config')
        localStorage.removeItem('videoPublish_selectedVideos')
      }
    },

    // 保存执行状态
    saveExecutionState() {
      try {
        // 保存执行状态
        const state = {
          deviceId: this.deviceId,
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted,
          currentTaskId: this.currentTaskId,
          currentLogId: this.currentLogId,
          currentStatus: this.currentStatus,
          currentStep: this.currentStep,
          publishedVideoCount: this.publishedVideoCount,
          totalVideoCount: this.totalVideoCount,
          errorMessage: this.errorMessage,
          timestamp: Date.now()
        }
        localStorage.setItem(`videoPublish_executionState_${this.deviceId}`, JSON.stringify(state))

        // 保存配置参数
        localStorage.setItem(`videoPublish_config_${this.deviceId}`, JSON.stringify(this.config))

        // 保存选中的视频
        localStorage.setItem(`videoPublish_selectedVideos_${this.deviceId}`, JSON.stringify(this.selectedVideos))

        // 保存视频传输进度
        const transferProgress = {
          ...this.videoTransferProgress,
          timestamp: Date.now()
        }
        localStorage.setItem(`videoPublish_transferProgress_${this.deviceId}`, JSON.stringify(transferProgress))

        // 保存下载进度
        const downloadProgress = {
          ...this.downloadProgress,
          timestamp: Date.now()
        }
        localStorage.setItem(`videoPublish_downloadProgress_${this.deviceId}`, JSON.stringify(downloadProgress))

        console.log('💾 [状态保存] 执行状态、配置参数、视频选择、传输进度和下载进度已保存')
      } catch (error) {
        console.error('❌ [状态保存] 保存状态失败:', error)
      }
    },

    // 清除执行状态
    clearExecutionState() {
      try {
        if (this.deviceId) {
          this.clearDeviceExecutionState()
        } else {
          // 如果没有设备ID，清除所有相关状态
          const keys = Object.keys(localStorage)
          keys.forEach(key => {
            if (key.startsWith('videoPublish_')) {
              localStorage.removeItem(key)
            }
          })
          console.log('🗑️ [状态清除] 所有视频发布相关状态已清除')
        }
      } catch (error) {
        console.error('❌ [状态清除] 清除状态失败:', error)
      }
    },

    // 清除特定设备的执行状态
    clearDeviceExecutionState() {
      try {
        if (!this.deviceId) {
          console.warn('⚠️ [设备状态清除] 设备ID为空，无法清除')
          return
        }

        localStorage.removeItem(`videoPublish_executionState_${this.deviceId}`)
        localStorage.removeItem(`videoPublish_config_${this.deviceId}`)
        localStorage.removeItem(`videoPublish_selectedVideos_${this.deviceId}`)
        localStorage.removeItem(`videoPublish_transferProgress_${this.deviceId}`)
        localStorage.removeItem(`videoPublish_downloadProgress_${this.deviceId}`)

        console.log(`🗑️ [设备状态清除] 设备 ${this.deviceId} 的所有状态已清除`)
      } catch (error) {
        console.error('❌ [设备状态清除] 清除设备状态失败:', error)
      }
    },

    // 停止脚本
    async stopScript() {
      console.log('[VideoPublishConfig] 停止脚本开始')
      console.log('[VideoPublishConfig] 当前状态:', {
        deviceId: this.deviceId,
        currentLogId: this.currentLogId,
        currentTaskId: this.currentTaskId,
        isScriptRunning: this.isScriptRunning
      })

      if (!this.deviceId) {
        console.error('[VideoPublishConfig] 设备ID为空，无法停止脚本')
        this.$message.error('设备ID为空，无法停止脚本')
        return
      }

      if (!this.currentLogId && !this.currentTaskId) {
        console.warn('[VideoPublishConfig] logId和taskId都为空，但仍尝试停止')
      }

      // 立即更新本地状态显示
      this.isScriptRunning = false
      this.isScriptCompleted = false
      this.currentStatus = '正在停止...'
      this.currentStep = '发送停止命令'
      this.errorMessage = ''

      // 保存状态更新
      this.saveExecutionState()

      try {
        // 直接调用停止API，就像执行日志页面一样
        const stopParams = {
          taskId: this.currentTaskId,
          deviceId: this.deviceId,
          logId: this.currentLogId
        }

        console.log('[VideoPublishConfig] 直接调用停止API，参数:', stopParams)
        console.log('[VideoPublishConfig] API URL: /api/xiaohongshu/stop')

        const response = await this.$http.post('/api/xiaohongshu/stop', stopParams)

        if (response.data.success) {
          console.log('[VideoPublishConfig] 停止命令发送成功')
          this.$message.success('停止命令已发送')

          // 更新状态为停止中
          this.currentStatus = '正在停止...'
          this.currentStep = '脚本正在停止'

        } else {
          console.error('[VideoPublishConfig] 停止命令发送失败:', response.data.message)
          this.$message.error(response.data.message || '停止失败')

          // 恢复运行状态
          this.isScriptRunning = true
          this.currentStatus = '执行中'
        }
      } catch (error) {
        console.error('[VideoPublishConfig] 停止脚本失败:', error)
        this.$message.error('停止失败: ' + (error.response?.data?.message || error.message))

        // 恢复运行状态
        this.isScriptRunning = true
        this.currentStatus = '执行中'
      }

      // 更新Vuex状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'videoPublish',
        stateData: {
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: false,
          config: this.config
        }
      })

      // 保存最终状态
      this.saveExecutionState()
    },



    resetExecutionState() {
      console.log('[VideoPublishConfig] 重置执行状态')

      this.isScriptRunning = false
      this.isScriptCompleted = false
      this.currentStatus = '等待开始'
      this.currentStep = '等待开始'
      this.publishedVideoCount = 0
      this.errorMessage = ''
      this.currentLogId = null
      this.currentTaskId = null

      // 重置传输进度
      this.videoTransferProgress = {
        isTransferring: false,
        currentVideoIndex: 0,
        totalVideos: 0,
        currentVideoName: '',
        currentVideoProgress: 0,
        currentVideoSize: 0,
        currentVideoTotalSize: 0,
        transferSpeed: 0,
        estimatedTime: 0,
        taskId: '',
        deviceId: '',
        status: 'pending'
      }

      // 重置下载进度
      this.downloadProgress = {
        isDownloading: false,
        percentage: 0,
        downloadedSize: '0MB',
        totalSize: '0MB',
        speed: '0MB/s',
        message: '准备下载...'
      }

      // 清除保存的执行状态
      this.clearDeviceExecutionState()

      console.log('[VideoPublishConfig] 执行状态重置完成')
    },

    // 恢复进度信息
    restoreProgressInfo() {
      console.log('📊 [进度恢复] 开始恢复进度信息，设备ID:', this.deviceId)

      if (!this.deviceId) {
        console.log('⚠️ [进度恢复] 设备ID为空，跳过进度恢复')
        return
      }

      // 恢复视频传输进度
      const savedTransferProgress = localStorage.getItem(`videoPublish_transferProgress_${this.deviceId}`)
      if (savedTransferProgress) {
        try {
          const transferProgress = JSON.parse(savedTransferProgress)
          console.log('📋 [进度恢复] 找到保存的传输进度:', transferProgress)

          // 检查传输进度是否仍然有效（不超过1小时）
          const progressAge = Date.now() - transferProgress.timestamp
          if (progressAge < 60 * 60 * 1000) { // 1小时内
            // 恢复传输进度，但移除时间戳
            const { timestamp, ...progressData } = transferProgress
            this.videoTransferProgress = { ...this.videoTransferProgress, ...progressData }
            console.log('✅ [进度恢复] 视频传输进度已恢复:', this.videoTransferProgress)
          } else {
            console.log('⏰ [进度恢复] 传输进度已过期，清除保存的进度')
            localStorage.removeItem(`videoPublish_transferProgress_${this.deviceId}`)
          }
        } catch (error) {
          console.error('❌ [进度恢复] 恢复传输进度失败:', error)
        }
      } else {
        console.log('📋 [进度恢复] 没有找到保存的传输进度')
      }

      // 恢复下载进度
      const savedDownloadProgress = localStorage.getItem(`videoPublish_downloadProgress_${this.deviceId}`)
      if (savedDownloadProgress) {
        try {
          const downloadProgress = JSON.parse(savedDownloadProgress)
          console.log('📋 [进度恢复] 找到保存的下载进度:', downloadProgress)

          // 检查下载进度是否仍然有效（不超过1小时）
          const progressAge = Date.now() - downloadProgress.timestamp
          if (progressAge < 60 * 60 * 1000) { // 1小时内
            // 恢复下载进度，但移除时间戳
            const { timestamp, ...progressData } = downloadProgress
            this.downloadProgress = { ...this.downloadProgress, ...progressData }
            console.log('✅ [进度恢复] 下载进度已恢复:', this.downloadProgress)
          } else {
            console.log('⏰ [进度恢复] 下载进度已过期，清除保存的进度')
            localStorage.removeItem(`videoPublish_downloadProgress_${this.deviceId}`)
          }
        } catch (error) {
          console.error('❌ [进度恢复] 恢复下载进度失败:', error)
        }
      } else {
        console.log('📋 [进度恢复] 没有找到保存的下载进度')
      }

      console.log('📊 [进度恢复] 进度信息恢复完成')
    },

    // 检查执行日志状态
    async checkExecutionLogStatus() {
      console.log('[VideoPublishConfig] 检查执行日志状态')

      if (!this.currentLogId) {
        console.warn('[VideoPublishConfig] 没有logId，无法检查执行日志')
        this.verifyDeviceExecutionState()
        return
      }

      try {
        // 查询执行日志状态
        const response = await this.$http.get(`/api/xiaohongshu/logs/${this.currentLogId}`)
        const logData = response.data.data

        if (logData && logData.status) {
          console.log(`[VideoPublishConfig] 执行日志状态: ${logData.status}`)

          if (logData.status === 'completed' || logData.status === 'failed') {
            console.log(`[VideoPublishConfig] 脚本已完成，状态: ${logData.status}，重置为初始状态`)
            // 脚本已完成，重置为初始状态而不是显示"执行完成"
            this.resetExecutionState()

            // 清除保存的执行状态
            this.clearDeviceExecutionState()
          } else {
            console.log(`[VideoPublishConfig] 脚本仍在执行，状态: ${logData.status}`)
            // 脚本仍在执行，验证设备状态
            this.verifyDeviceExecutionState()
          }
        } else {
          console.warn('[VideoPublishConfig] 未找到执行日志，验证设备状态')
          this.verifyDeviceExecutionState()
        }
      } catch (error) {
        if (error.response && error.response.status === 404) {
          console.warn('[VideoPublishConfig] 执行日志不存在（可能是taskId格式问题），跳过日志检查')
        } else if (error.response && error.response.status === 403) {
          // 🔥 403权限错误的详细信息
          const errorMessage = error.response.data?.message || '权限验证失败'
          console.error('[VideoPublishConfig] 权限验证失败:', errorMessage)

          // 🔥 使用弹窗显示详细的权限错误信息
          alert(`执行日志权限验证失败:\n错误信息: ${errorMessage}\n查询的logId: ${this.currentLogId}\n请检查用户权限设置`)
        } else {
          console.error('[VideoPublishConfig] 检查执行日志失败:', error)
        }
        // 检查失败，回退到设备状态验证
        this.verifyDeviceExecutionState()
      }
    },

    // 验证设备执行状态
    verifyDeviceExecutionState() {
      console.log('[VideoPublishConfig] 验证设备执行状态')

      if (!this.deviceId) {
        console.warn('[VideoPublishConfig] 设备ID为空，无法验证状态')
        return
      }

      // 检查设备是否在线且忙碌
      const devices = this.$store.getters['device/devices']
      const currentDevice = devices.find(device => device.device_id === this.deviceId)

      if (!currentDevice) {
        console.warn(`[VideoPublishConfig] 未找到设备 ${this.deviceId}，可能已离线`)
        this.handleDeviceOffline()
        return
      }

      console.log(`[VideoPublishConfig] 设备 ${this.deviceId} 当前状态:`, currentDevice.status)

      if (currentDevice.status !== 'busy') {
        console.warn(`[VideoPublishConfig] 设备 ${this.deviceId} 状态为 ${currentDevice.status}，不是忙碌状态`)

        // 不要立即重置执行状态，因为脚本可能已经执行完成但前端还没收到完成事件
        // 只有在设备离线时才重置状态
        if (currentDevice.status === 'offline') {
          console.warn(`[VideoPublishConfig] 设备离线，重置执行状态`)
          this.resetExecutionState()
        } else {
          console.log(`[VideoPublishConfig] 设备状态为 ${currentDevice.status}，保持当前执行状态`)
        }
        return
      }

      // 设备状态正常，恢复监听
      console.log(`[VideoPublishConfig] 设备 ${this.deviceId} 状态验证通过，恢复监听`)
      this.startProgressMonitoring()
    },

    // 处理设备离线
    handleDeviceOffline() {
      console.log(`[VideoPublishConfig] 处理设备 ${this.deviceId} 离线`)

      // 重置执行状态
      this.isScriptRunning = false
      this.isScriptCompleted = false
      this.currentStatus = '设备离线'
      this.currentStep = '设备已断开连接'
      this.errorMessage = '设备已离线'

      // 保存状态更新
      this.saveExecutionState()

      this.$message.warning(`设备 ${this.deviceId} 已离线，执行状态已重置`)
    },

    // 开始进度监控
    startProgressMonitoring() {
      console.log('[VideoPublishConfig] 开始进度监控')

      // 这里可以添加进度监控的逻辑
      // 比如定期检查任务状态、监听WebSocket事件等

      // 如果有Socket连接，确保监听相关事件
      if (this.socket) {
        console.log('[VideoPublishConfig] Socket已连接，监听进度事件')
      } else {
        console.log('[VideoPublishConfig] 重新初始化Socket连接')
        this.initSocketConnection()
      }
    },

    formatFileSize(bytes) {
      // 确保输入是数字类型
      const numBytes = parseInt(bytes) || 0
      if (numBytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(numBytes) / Math.log(k))
      return parseFloat((numBytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    formatTime(timeString) {
      if (!timeString) return '-'
      return new Date(timeString).toLocaleString('zh-CN')
    },

    formatDuration(seconds) {
      if (!seconds) return '0:00'
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins}:${secs.toString().padStart(2, '0')}`
    },

    // 获取状态样式类
    getStatusClass(status) {
      if (status.includes('error') || status.includes('失败')) {
        return 'status-error'
      } else if (status.includes('completed') || status.includes('完成')) {
        return 'status-success'
      } else if (status.includes('warning') || status.includes('警告')) {
        return 'status-warning'
      } else {
        return 'status-normal'
      }
    },

    // 获取进度百分比
    getProgressPercentage() {
      if (this.totalVideoCount === 0) return 0
      return Math.round((this.publishedVideoCount / this.totalVideoCount) * 100)
    },

    // 获取进度条状态
    getProgressStatus() {
      if (this.errorMessage) {
        return 'exception'
      } else if (this.publishedVideoCount === this.totalVideoCount && this.totalVideoCount > 0) {
        return 'success'
      } else {
        return null
      }
    },

    // 显示单个视频传输信息
    async showVideoTransferInfo(video) {
      try {
        console.log('🎯🎯🎯 [VideoPublishConfig] ===== 接收到show-transfer-info事件 ===== 🎯🎯🎯')
        console.log('🔍 [VideoPublishConfig] 显示视频传输信息:', video)
        console.log('🔍 [VideoPublishConfig] 视频ID:', video.id, '视频名称:', video.original_name)
        console.log('🔍 [VideoPublishConfig] 方法被调用，开始处理...')
        console.log('🔍 [VideoPublishConfig] 当前弹出框状态:', this.transferInfoDialogVisible)

        // 强制显示弹出框，先设置基本信息
        this.transferInfoDialogVisible = true
        this.transferInfoDialogTitle = `视频传输信息 - ${video.original_name}`
        console.log('🎯 [VideoPublishConfig] 弹出框已强制显示，标题:', this.transferInfoDialogTitle)
        console.log('🎯 [VideoPublishConfig] 弹出框状态已设置为:', this.transferInfoDialogVisible)

        this.currentVideoTransferInfo = video
        this.allVideosTransferInfo = null

        // 获取视频传输记录
        console.log('📡 [VideoPublishConfig] 请求传输记录:', `/api/videos/${video.id}/transfer-records`)

        // 获取认证token
        const token = localStorage.getItem('token')

        const response = await this.$http.get(`/api/videos/${video.id}/transfer-records`, {
          headers: {
            ...(token ? { 'Authorization': `Bearer ${token}` } : {})
          }
        })
        console.log('📊 [VideoPublishConfig] 传输记录响应:', response.data)

        if (response.data.success) {
          this.transferRecords = response.data.records || []
          this.transferStatistics = response.data.statistics || {
            totalTransfers: 0,
            uniqueDevices: 0,
            successRate: 0
          }
          console.log('✅ [VideoPublishConfig] 传输记录设置完成:', this.transferRecords)
          console.log('📈 [VideoPublishConfig] 传输统计:', this.transferStatistics)
        } else {
          console.error('❌ [VideoPublishConfig] API返回失败:', response.data.message)
        }

        console.log('🎯 [VideoPublishConfig] 准备显示弹出框，当前状态:', this.transferInfoDialogVisible)

        // 使用nextTick确保DOM更新
        this.$nextTick(() => {
          console.log('🎯 [VideoPublishConfig] nextTick - 弹出框应该已显示')
        })

      } catch (error) {
        console.error('❌ [VideoPublishConfig] 获取视频传输信息失败:', error)
        this.$message.error('获取传输信息失败: ' + error.message)
      }
    },







    // 显示所有视频传输信息汇总
    async showAllVideosTransferInfo() {
      try {
        this.transferInfoDialogTitle = '所有视频传输信息汇总'
        this.currentVideoTransferInfo = null

        // 获取所有选中视频的传输信息
        const videoIds = this.selectedVideos.map(v => v.id)
        const response = await this.$http.post('/api/videos/transfer-summary', {
          videoIds: videoIds
        })

        if (response.data.success) {
          this.allVideosTransferInfo = response.data.videos || []
          this.allTransferStatistics = response.data.statistics || {
            totalVideos: 0,
            totalTransfers: 0,
            uniqueDevices: 0,
            successRate: 0
          }
        }

        this.transferInfoDialogVisible = true
      } catch (error) {
        console.error('获取视频传输汇总信息失败:', error)
        this.$message.error('获取传输汇总信息失败: ' + error.message)
      }
    },

    // 获取传输状态文本
    getTransferStatusText(status) {
      const statusMap = {
        'pending': '等待中',
        'transferring': '传输中',
        'in_progress': '传输中',
        'completed': '已完成',
        'failed': '失败',
        'success': '成功',
        'error': '错误'
      }
      return statusMap[status] || status
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },



    // 加载传输记录
    async loadTransferRecords() {
      try {
        this.transferRecordsLoading = true

        const token = localStorage.getItem('token')
        const params = {
          page: this.transferRecordsPagination.currentPage,
          pageSize: this.transferRecordsPagination.pageSize,
          videoFilename: this.transferRecordsFilter.videoFilename,
          deviceId: this.transferRecordsFilter.deviceId,
          status: this.transferRecordsFilter.status
        }

        const response = await this.$http.get('/api/xiaohongshu/transfer-records', {
          params,
          headers: {
            ...(token ? { 'Authorization': `Bearer ${token}` } : {})
          }
        })

        if (response.data.success) {
          this.transferRecordsList = response.data.data.records
          this.transferRecordsPagination.total = response.data.data.total
          console.log('✅ [传输记录] 加载成功，共', response.data.data.total, '条记录')
        } else {
          this.$message.error('加载传输记录失败: ' + response.data.message)
        }

      } catch (error) {
        console.error('❌ [传输记录] 加载失败:', error)
        this.$message.error('加载传输记录失败: ' + error.message)
      } finally {
        this.transferRecordsLoading = false
      }
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.transferRecordsPagination.pageSize = val
      this.transferRecordsPagination.currentPage = 1
      this.loadTransferRecords()
    },

    // 处理当前页变化
    handleCurrentChange(val) {
      this.transferRecordsPagination.currentPage = val
      this.loadTransferRecords()
    },

    // 解析下载进度信息
    parseDownloadProgress(data) {
      try {
        // 检查是否是下载步骤（支持多种下载步骤名称）
        if ((data.currentStep === '下载文件' || data.currentStep === '下载视频文件' || data.currentStep === '连接服务器') && data.message) {
          // 解析消息格式："正在下载: 24.9MB / 611.6MB"
          const downloadMatch = data.message.match(/正在下载:\s*([0-9.]+)MB\s*\/\s*([0-9.]+)MB/)
          if (downloadMatch) {
            const downloadedMB = parseFloat(downloadMatch[1])
            const totalMB = parseFloat(downloadMatch[2])

            // 计算下载百分比
            const percentage = totalMB > 0 ? Math.floor((downloadedMB / totalMB) * 100) : 0

            // 更新下载进度
            this.downloadProgress = {
              isDownloading: true,
              percentage: percentage,
              downloadedSize: downloadedMB.toFixed(1) + 'MB',
              totalSize: totalMB.toFixed(1) + 'MB',
              speed: this.calculateDownloadSpeed(downloadedMB, totalMB),
              message: `正在下载: ${downloadedMB.toFixed(1)}MB / ${totalMB.toFixed(1)}MB`
            }

            console.log('📥 [下载进度] 更新下载进度:', this.downloadProgress)
          }
        } else if (data.currentStep !== '下载文件') {
          // 非下载步骤时隐藏下载进度
          if (this.downloadProgress.isDownloading) {
            this.downloadProgress.isDownloading = false
            console.log('📥 [下载进度] 隐藏下载进度条')
          }
        }
      } catch (error) {
        console.error('❌ [下载进度] 解析下载进度失败:', error)
      }
    },

    // 计算下载速度（简单估算）
    calculateDownloadSpeed(downloadedMB, totalMB) {
      try {
        // 基于当前进度估算速度
        if (!this.downloadStartTime) {
          this.downloadStartTime = Date.now()
          return '计算中...'
        }

        const elapsedSeconds = (Date.now() - this.downloadStartTime) / 1000
        if (elapsedSeconds > 0) {
          const speedMBps = downloadedMB / elapsedSeconds
          if (speedMBps >= 1) {
            return speedMBps.toFixed(1) + 'MB/s'
          } else {
            return (speedMBps * 1024).toFixed(0) + 'KB/s'
          }
        }
        return '计算中...'
      } catch (error) {
        return '未知'
      }
    },

    // 获取当前配置（用于配置复制功能）
    getConfig() {
      console.log('📋 [VideoPublishConfig] 获取配置，设备ID:', this.deviceId)
      console.log('📋 [VideoPublishConfig] 当前配置:', this.config)

      // 返回配置的副本，排除视频选择（按照用户要求，视频选择不复制）
      const configCopy = {
        selectedApp: this.config.selectedApp,
        titleTemplate: this.config.titleTemplate,
        videoDescription: this.config.videoDescription,
        hashtags: this.config.hashtags,
        publishOptions: [...this.config.publishOptions],
        operationDelay: this.config.operationDelay,
        retryCount: this.config.retryCount
      }

      console.log('📋 [VideoPublishConfig] 返回配置副本:', configCopy)
      return configCopy
    },

    // 设置配置（用于配置复制功能）
    setConfig(config) {
      console.log('📋 [VideoPublishConfig] 设置配置，设备ID:', this.deviceId)
      console.log('📋 [VideoPublishConfig] 接收到的配置:', config)
      console.log('📋 [VideoPublishConfig] 设置前的配置:', this.config)

      if (config) {
        // 只复制指定的配置项，不包括视频选择
        this.config = {
          ...this.config,
          selectedApp: config.selectedApp || this.config.selectedApp,
          titleTemplate: config.titleTemplate || this.config.titleTemplate,
          videoDescription: config.videoDescription || this.config.videoDescription,
          hashtags: config.hashtags || this.config.hashtags,
          publishOptions: config.publishOptions ? [...config.publishOptions] : this.config.publishOptions,
          operationDelay: config.operationDelay !== undefined ? config.operationDelay : this.config.operationDelay,
          retryCount: config.retryCount !== undefined ? config.retryCount : this.config.retryCount
        }

        console.log('📋 [VideoPublishConfig] 设置后的配置:', this.config)

        // 强制触发Vue的响应式更新
        this.$forceUpdate()

        // 触发配置变化事件和保存配置
        this.$nextTick(() => {
          this.onInputChange()
          console.log('✅ [VideoPublishConfig] 配置已更新并保存')
        })
      }
    }
  }
}
</script>

<style scoped>
.video-publish-config {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.execution-status {
  margin-bottom: 20px;
}

.video-management-section {
  margin-bottom: 30px;

  .video-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .selected-videos-preview {
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    padding: 15px;
    background: #fafafa;

    h5 {
      margin: 0 0 15px 0;
      color: #303133;
    }

    .video-preview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .video-preview-item {
      transition: all 0.3s;

      .video-preview-container {
        position: relative;
        width: 100%;
        height: 120px;
        background: #f5f7fa;
      }

      .video-thumbnail {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
        border-radius: 4px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .no-thumbnail {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #c0c4cc;

          i {
            font-size: 32px;
          }
        }

        .duration-badge {
          position: absolute;
          bottom: 4px;
          right: 4px;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
        }

        .play-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          opacity: 0;
          transition: opacity 0.2s;

          .play-button {
            width: 40px;
            height: 40px;
            font-size: 16px;
            background: rgba(64, 158, 255, 0.9);
            border: none;

            &:hover {
              background: rgba(64, 158, 255, 1);
              transform: scale(1.1);
            }
          }
        }

        &:hover .play-overlay {
          opacity: 1;
        }
      }

      .video-info {
        .video-name {
          font-weight: 500;
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 13px;
          color: #303133;
        }

        .video-meta {
          margin-bottom: 8px;

          .el-tag {
            margin-right: 4px;
          }
        }
      }
    }

    .more-videos-card {
      .more-videos-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 120px;
        text-align: center;

        .more-count {
          margin-bottom: 8px;
        }

        .more-text {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }
      }
    }

    .selection-summary {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #ebeef5;

      .summary-info {
        flex: 1;
      }

      .summary-actions {
        margin-left: 15px;
      }
    }
  }

  .no-videos-selected {
    text-align: center;
    padding: 40px;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 10px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.config-form {
  margin-top: 20px;
}

/* 实时状态显示样式 */
.realtime-status-section {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  h4 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
  }

  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: white;
      border-radius: 4px;
      border: 1px solid #e4e7ed;

      &.error {
        background: #fef0f0;
        border-color: #fbc4c4;
      }

      .status-label {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }

      .status-value {
        font-size: 14px;
        font-weight: 600;

        &.status-error {
          color: #f56c6c;
        }

        &.status-success {
          color: #67c23a;
        }

        &.status-warning {
          color: #e6a23c;
        }

        &.status-normal {
          color: #409eff;
        }
      }
    }
  }

  .progress-section {
    margin-top: 15px;
  }

  .transfer-progress-section {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 15px 0;
      color: #409EFF;
      font-size: 16px;
    }

    .download-progress {
      margin-bottom: 20px;
      padding: 15px;
      background: #f0f9ff;
      border-radius: 6px;
      border: 1px solid #bfdbfe;
    }

    .transfer-info {
      margin-bottom: 15px;

      .transfer-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .transfer-label {
          font-weight: 600;
          color: #606266;
        }

        .transfer-value {
          color: #303133;
        }
      }
    }

    .video-progress {
      .progress-details {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;
        font-size: 12px;
        color: #909399;

        .size-info {
          font-weight: 600;
        }

        .speed-info {
          color: #67C23A;
        }

        .time-info {
          color: #E6A23C;
        }
      }
    }
  }
}

/* 传输信息弹出框样式 */
.video-transfer-info {
  .video-basic-info {
    margin-bottom: 20px;

    .info-item {
      margin-bottom: 10px;

      .info-label {
        font-weight: 600;
        color: #606266;
      }

      .info-value {
        color: #303133;
      }
    }
  }

  .transfer-statistics {
    margin-bottom: 20px;
  }

  .stat-card {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 12px;
      color: #909399;
    }
  }
}

.all-videos-transfer-info {
  .summary-statistics {
    margin-bottom: 20px;
  }

  .stat-card {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .stat-number {
      font-size: 20px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 12px;
      color: #909399;
    }
  }
}

/* 视频预览项的操作按钮样式 */
.video-preview-item {
  .video-actions {
    margin-top: 5px;
    text-align: center;
  }
}
</style>
