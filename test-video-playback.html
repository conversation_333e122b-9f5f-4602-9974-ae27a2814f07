<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .video-container {
            margin: 15px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            overflow: hidden;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频播放测试</h1>
        <p>测试视频文件是否可以正常播放</p>

        <div class="test-section">
            <h3>📹 直接视频播放测试</h3>
            <div class="video-container">
                <video id="video1" controls preload="metadata">
                    <source src="http://localhost:3002/uploads/videos/video-1754998361117-259859642.mp4" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
            </div>
            <div class="status info" id="video1-status">等待测试...</div>
            <button class="test-button" onclick="testVideo('video1')">测试播放</button>
            <button class="test-button" onclick="checkVideoInfo('video1')">检查视频信息</button>
        </div>

        <div class="test-section">
            <h3>📹 第二个视频测试</h3>
            <div class="video-container">
                <video id="video2" controls preload="metadata">
                    <source src="http://localhost:3002/uploads/videos/video-1754998453765-64244105.mp4" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
            </div>
            <div class="status info" id="video2-status">等待测试...</div>
            <button class="test-button" onclick="testVideo('video2')">测试播放</button>
            <button class="test-button" onclick="checkVideoInfo('video2')">检查视频信息</button>
        </div>

        <div class="test-section">
            <h3>🔧 网络请求测试</h3>
            <div id="network-test-results">
                <div class="status info">点击按钮开始测试...</div>
            </div>
            <button class="test-button" onclick="testNetworkAccess()">测试网络访问</button>
        </div>

        <div class="test-section">
            <h3>📊 浏览器支持信息</h3>
            <div id="browser-info"></div>
        </div>
    </div>

    <script>
        // 显示浏览器信息
        function showBrowserInfo() {
            const video = document.createElement('video');
            const info = {
                userAgent: navigator.userAgent,
                canPlayMP4: video.canPlayType('video/mp4'),
                canPlayWebM: video.canPlayType('video/webm'),
                canPlayOgg: video.canPlayType('video/ogg'),
                videoSupport: !!video.canPlayType
            };

            document.getElementById('browser-info').innerHTML = `
                <div class="status info">
                    <strong>用户代理:</strong> ${info.userAgent}<br>
                    <strong>MP4支持:</strong> ${info.canPlayMP4 || '不支持'}<br>
                    <strong>WebM支持:</strong> ${info.canPlayWebM || '不支持'}<br>
                    <strong>Ogg支持:</strong> ${info.canPlayOgg || '不支持'}<br>
                    <strong>视频API支持:</strong> ${info.videoSupport ? '是' : '否'}
                </div>
            `;
        }

        // 测试视频播放
        function testVideo(videoId) {
            const video = document.getElementById(videoId);
            const status = document.getElementById(videoId + '-status');
            
            status.innerHTML = '正在测试播放...';
            status.className = 'status info';

            video.addEventListener('loadedmetadata', function() {
                status.innerHTML = `✅ 视频元数据加载成功<br>
                    时长: ${video.duration.toFixed(2)}秒<br>
                    尺寸: ${video.videoWidth}x${video.videoHeight}`;
                status.className = 'status success';
            }, { once: true });

            video.addEventListener('canplay', function() {
                status.innerHTML += '<br>✅ 视频可以开始播放';
            }, { once: true });

            video.addEventListener('error', function(e) {
                const error = video.error;
                let errorMsg = '未知错误';
                if (error) {
                    switch(error.code) {
                        case error.MEDIA_ERR_ABORTED:
                            errorMsg = '播放被中止';
                            break;
                        case error.MEDIA_ERR_NETWORK:
                            errorMsg = '网络错误';
                            break;
                        case error.MEDIA_ERR_DECODE:
                            errorMsg = '解码错误';
                            break;
                        case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                            errorMsg = '不支持的媒体格式或源';
                            break;
                    }
                }
                status.innerHTML = `❌ 视频加载失败: ${errorMsg}`;
                status.className = 'status error';
            }, { once: true });

            // 尝试加载视频
            video.load();
        }

        // 检查视频信息
        function checkVideoInfo(videoId) {
            const video = document.getElementById(videoId);
            const status = document.getElementById(videoId + '-status');
            
            const info = {
                src: video.currentSrc || video.src,
                readyState: video.readyState,
                networkState: video.networkState,
                error: video.error
            };

            const readyStates = ['HAVE_NOTHING', 'HAVE_METADATA', 'HAVE_CURRENT_DATA', 'HAVE_FUTURE_DATA', 'HAVE_ENOUGH_DATA'];
            const networkStates = ['NETWORK_EMPTY', 'NETWORK_IDLE', 'NETWORK_LOADING', 'NETWORK_NO_SOURCE'];

            status.innerHTML = `
                <strong>视频信息:</strong><br>
                源地址: ${info.src}<br>
                就绪状态: ${readyStates[info.readyState]} (${info.readyState})<br>
                网络状态: ${networkStates[info.networkState]} (${info.networkState})<br>
                错误: ${info.error ? info.error.message : '无'}
            `;
            status.className = 'status info';
        }

        // 测试网络访问
        async function testNetworkAccess() {
            const results = document.getElementById('network-test-results');
            results.innerHTML = '<div class="status info">正在测试网络访问...</div>';

            const testUrls = [
                'http://localhost:3002/uploads/videos/video-1754998361117-259859642.mp4',
                'http://localhost:3002/uploads/videos/video-1754998453765-64244105.mp4',
                'http://localhost:3002/thumbnails/thumb_video-1754998361117-259859642.svg'
            ];

            let html = '';
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    const className = response.ok ? 'success' : 'error';
                    html += `
                        <div class="status ${className}">
                            <strong>URL:</strong> ${url}<br>
                            <strong>状态:</strong> ${response.status} ${response.statusText}<br>
                            <strong>Content-Type:</strong> ${response.headers.get('content-type') || 'N/A'}<br>
                            <strong>Content-Length:</strong> ${response.headers.get('content-length') || 'N/A'}
                        </div>
                    `;
                } catch (error) {
                    html += `
                        <div class="status error">
                            <strong>URL:</strong> ${url}<br>
                            <strong>错误:</strong> ${error.message}
                        </div>
                    `;
                }
            }
            results.innerHTML = html;
        }

        // 页面加载时显示浏览器信息
        window.addEventListener('load', function() {
            showBrowserInfo();
            
            // 自动测试第一个视频
            setTimeout(() => {
                testVideo('video1');
            }, 1000);
        });
    </script>
</body>
</html>
