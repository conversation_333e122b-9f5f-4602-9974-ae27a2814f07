const path = require('path');

// 测试路径处理逻辑
function getVideoDeletePaths(filePath, baseDir = __dirname) {
  const possiblePaths = [
    filePath, // 原始路径
    // 如果是旧路径（包含core），转换为新路径
    filePath.includes('\\core\\uploads\\') || filePath.includes('/core/uploads/') ? 
      filePath.replace(/[\\\/]core[\\\/]uploads[\\\/]/g, '/uploads/') : null,
    // 如果是绝对路径，尝试提取文件名并构建新路径
    path.isAbsolute(filePath) ? 
      path.join(baseDir, '../uploads/videos', path.basename(filePath)) : null,
    // 如果是相对路径，构建完整路径
    !path.isAbsolute(filePath) && filePath.startsWith('/uploads/') ?
      path.join(baseDir, '..', filePath) : null
  ].filter(Boolean); // 过滤掉null值

  return possiblePaths;
}

// 测试用例
const testCases = [
  'D:\\脚本\\群控\\server\\core\\uploads\\videos\\video-old-1.mp4',
  'D:\\脚本\\群控\\server\\uploads\\videos\\video-new-1.mp4',
  '/uploads/videos/video-relative-1.mp4'
];

console.log('🧪 路径处理测试结果:\n');

testCases.forEach((testPath, index) => {
  console.log(`测试 ${index + 1}: ${testPath}`);
  const paths = getVideoDeletePaths(testPath);
  console.log('生成的候选路径:');
  paths.forEach((p, i) => {
    console.log(`  ${i + 1}. ${p}`);
  });
  console.log('');
});
