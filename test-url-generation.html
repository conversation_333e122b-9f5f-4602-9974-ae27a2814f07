<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL生成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 URL生成逻辑测试</h1>
        
        <div class="test-section">
            <h3>环境信息</h3>
            <div class="test-result">
                <strong>当前URL:</strong> <span id="current-url"></span><br>
                <strong>主机名:</strong> <span id="hostname"></span><br>
                <strong>端口:</strong> <span id="port"></span><br>
                <strong>协议:</strong> <span id="protocol"></span>
            </div>
        </div>

        <div class="test-section">
            <h3>模拟getApiBaseUrl函数</h3>
            <div class="test-result" id="api-base-url-result"></div>
        </div>

        <div class="test-section">
            <h3>缩略图URL生成测试</h3>
            <div id="thumbnail-tests"></div>
        </div>

        <div class="test-section">
            <h3>视频URL生成测试</h3>
            <div id="video-tests"></div>
        </div>

        <div class="test-section">
            <h3>实际URL访问测试</h3>
            <div id="access-tests"></div>
        </div>
    </div>

    <script>
        // 更新环境信息
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('hostname').textContent = window.location.hostname;
        document.getElementById('port').textContent = window.location.port || '默认端口';
        document.getElementById('protocol').textContent = window.location.protocol;

        // 模拟getApiBaseUrl函数
        function getApiBaseUrl() {
            const currentHostname = window.location.hostname;
            const currentPort = window.location.port;
            
            console.log('🔧 [ServerConfig] 获取服务器URL，当前环境:', {
                NODE_ENV: 'production', // 模拟生产环境
                hostname: currentHostname,
                port: currentPort,
                protocol: window.location.protocol,
                fullUrl: window.location.href
            });

            // 检查是否为localhost访问
            const isLocalhostAccess = currentHostname === 'localhost' || currentHostname === '127.0.0.1';
            
            if (isLocalhostAccess) {
                console.log('🔧 [ServerConfig] 检测到localhost访问，使用代理模式');
                return ''; // 使用相对路径，通过代理访问
            }

            // 在生产环境或直接访问时，使用当前页面的协议和主机
            const protocol = window.location.protocol === 'https:' ? 'https' : 'http';
            const hostname = currentHostname;
            const port = currentPort || (protocol === 'https' ? '443' : '80');

            // 如果端口是3002，直接使用；否则假设服务器在3002端口
            const serverPort = port === '3002' ? port : '3002';

            const serverUrl = `${protocol}://${hostname}:${serverPort}`;
            console.log('🔧 [ServerConfig] 生成的服务器URL:', serverUrl);

            return serverUrl;
        }

        // 模拟getThumbnailUrl函数
        function getThumbnailUrl(thumbnailPath) {
            if (!thumbnailPath) return '';
            
            console.log('🖼️ 获取缩略图URL，输入路径:', thumbnailPath);
            
            // 如果已经是完整URL，直接返回
            if (thumbnailPath.startsWith('http')) {
                console.log('🖼️ 检测到完整URL，直接返回:', thumbnailPath);
                return thumbnailPath;
            }
            
            const baseUrl = getApiBaseUrl();
            
            // 确保路径以/开头
            const normalizedPath = thumbnailPath.startsWith('/') ? thumbnailPath : `/${thumbnailPath}`;
            
            // 如果baseUrl为空（代理模式），直接返回路径；否则拼接完整URL
            const fullUrl = baseUrl ? `${baseUrl}${normalizedPath}` : normalizedPath;
            
            console.log('🖼️ 生成缩略图URL:', fullUrl, '(baseUrl:', baseUrl, ')');
            return fullUrl;
        }

        // 模拟getVideoUrl函数
        function getVideoUrl(filePath) {
            if (!filePath) return '';

            console.log('🎬 获取视频URL，输入路径:', filePath);

            // 如果已经是完整URL，直接返回
            if (filePath.startsWith('http')) {
                console.log('🎬 检测到完整URL，直接返回:', filePath);
                return filePath;
            }

            const baseUrl = getApiBaseUrl();

            // 如果是相对路径（以/开头），转换为绝对路径
            if (filePath.startsWith('/')) {
                const fullUrl = baseUrl ? `${baseUrl}${filePath}` : filePath;
                console.log('🎬 相对路径转换为绝对路径:', fullUrl, '(baseUrl:', baseUrl, ')');
                return fullUrl;
            }

            // 处理完整文件系统路径，提取文件名
            const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || filePath;
            const relativePath = `/uploads/videos/${fileName}`;
            const fullUrl = baseUrl ? `${baseUrl}${relativePath}` : relativePath;
            console.log('🎬 从完整路径提取文件名，生成URL:', fullUrl, '(baseUrl:', baseUrl, ')');
            return fullUrl;
        }

        // 测试API Base URL
        const apiBaseUrl = getApiBaseUrl();
        document.getElementById('api-base-url-result').innerHTML = `
            <strong>getApiBaseUrl()结果:</strong> "${apiBaseUrl}"<br>
            <strong>说明:</strong> ${apiBaseUrl === '' ? '代理模式（localhost访问）' : '直接访问模式'}
        `;

        // 测试缩略图URL生成
        const thumbnailTests = [
            '/thumbnails/thumb_video-1754998361117-259859642.svg',
            'thumbnails/thumb_video-1754998453765-64244105.svg',
            'http://example.com/thumb.svg'
        ];

        let thumbnailHtml = '';
        thumbnailTests.forEach((path, index) => {
            const result = getThumbnailUrl(path);
            thumbnailHtml += `
                <div class="test-result">
                    <strong>测试${index + 1}:</strong><br>
                    输入: <code>${path}</code><br>
                    输出: <code>${result}</code>
                </div>
            `;
        });
        document.getElementById('thumbnail-tests').innerHTML = thumbnailHtml;

        // 测试视频URL生成
        const videoTests = [
            '/uploads/videos/video-1754998361117-259859642.mp4',
            'D:\\脚本\\群控\\server\\core\\uploads\\videos\\video-1754998453765-64244105.mp4',
            'http://example.com/video.mp4'
        ];

        let videoHtml = '';
        videoTests.forEach((path, index) => {
            const result = getVideoUrl(path);
            videoHtml += `
                <div class="test-result">
                    <strong>测试${index + 1}:</strong><br>
                    输入: <code>${path}</code><br>
                    输出: <code>${result}</code>
                </div>
            `;
        });
        document.getElementById('video-tests').innerHTML = videoHtml;

        // 测试实际URL访问
        async function testUrlAccess() {
            const testUrls = [
                '/thumbnails/thumb_video-1754998361117-259859642.svg',
                '/uploads/videos/video-1754998361117-259859642.mp4'
            ];

            let accessHtml = '';
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    const status = response.ok ? 'success' : 'error';
                    accessHtml += `
                        <div class="test-result ${status}">
                            <strong>URL:</strong> <code>${url}</code><br>
                            <strong>状态:</strong> ${response.status} ${response.statusText}<br>
                            <strong>Content-Type:</strong> ${response.headers.get('content-type') || 'N/A'}
                        </div>
                    `;
                } catch (error) {
                    accessHtml += `
                        <div class="test-result error">
                            <strong>URL:</strong> <code>${url}</code><br>
                            <strong>错误:</strong> ${error.message}
                        </div>
                    `;
                }
            }
            document.getElementById('access-tests').innerHTML = accessHtml;
        }

        // 延迟执行URL访问测试
        setTimeout(testUrlAccess, 1000);
    </script>
</body>
</html>
