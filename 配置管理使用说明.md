# Auto.js云群控系统配置管理使用说明

## 📋 概述

本文档详细说明如何配置Auto.js云群控系统的数据库账号密码、主站数据库连接以及硬编码IP地址的统一管理。

## 🎯 解决的问题

### 原有问题
1. **双数据库硬编码**：本地数据库和主站数据库的账号密码硬编码在代码中
2. **IP地址硬编码**：设备端脚本中硬编码了`************:3002`
3. **配置分散**：配置信息分散在多个文件中，难以统一管理
4. **部署困难**：每次部署都需要手动修改多个配置文件

### 解决方案
1. **统一环境变量配置**：使用`.env`文件集中管理所有配置
2. **自动化配置工具**：提供脚本自动生成和更新配置
3. **配置验证机制**：验证配置的完整性和正确性
4. **向后兼容**：保持与现有代码的兼容性

## 🚀 快速开始

### 方法一：自动化配置（推荐）

```bash
# 1. 运行配置生成工具
node generate-config.js

# 2. 验证配置
node validate-config.js

# 3. 部署系统
./deploy.sh
```

### 方法二：手动配置

```bash
# 1. 复制配置模板
cp .env.example .env

# 2. 编辑配置文件
nano .env

# 3. 验证配置
node validate-config.js
```

## 📁 配置文件结构

### 主要配置文件
```
├── .env                    # 主配置文件（需要创建）
├── .env.example           # 配置模板
├── config/
│   └── index.js          # 统一配置模块
├── generate-config.js     # 配置生成工具
├── validate-config.js     # 配置验证工具
└── ecosystem.config.js    # PM2配置文件
```

### 受影响的系统文件
```
├── server/config/database.js    # 数据库配置
├── scripts/双向.js              # 设备端脚本
├── web/src/utils/serverConfig.js # 前端服务器配置
└── nginx-autojs-control.conf    # Nginx配置
```

## 🔧 详细配置说明

### 1. 数据库配置

#### 本地数据库（必需）
存储系统数据：用户、设备、执行日志等
```bash
LOCAL_DB_HOST=localhost
LOCAL_DB_PORT=3306
LOCAL_DB_USER=autojs_control
LOCAL_DB_PASSWORD=你的强密码
LOCAL_DB_NAME=autojs_control
```

#### 主站数据库（可选）
用于账号验证，如果不配置将使用本地认证
```bash
MAIN_DB_HOST=你的主站数据库IP
MAIN_DB_PORT=3306
MAIN_DB_USER=你的主站数据库用户名
MAIN_DB_PASSWORD=你的主站数据库密码
MAIN_DB_NAME=zhuzhan
```

### 2. 服务器配置

```bash
# 服务器公网IP（重要）
SERVER_HOST=你的公网IP
PUBLIC_IP=你的公网IP

# 服务端口
SERVER_PORT=3002

# 运行环境
NODE_ENV=production
```

### 3. 安全配置

```bash
# JWT密钥（必须修改）
JWT_SECRET=使用64位随机字符串
JWT_EXPIRES_IN=24h

# 密码加密强度
BCRYPT_ROUNDS=10

# 登录安全
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
```

## 🛠️ 配置工具使用

### 1. 配置生成工具

```bash
node generate-config.js
```

**功能**：
- 交互式配置生成
- 自动生成强密码和JWT密钥
- 更新所有相关配置文件
- 生成部署脚本

**生成的文件**：
- `.env` - 环境配置文件
- `config/index.js` - 统一配置模块
- `ecosystem.config.js` - PM2配置
- `nginx-autojs-control.conf` - Nginx配置
- `deploy.sh` - 部署脚本

### 2. 配置验证工具

```bash
node validate-config.js
```

**验证项目**：
- 必需环境变量检查
- IP地址格式验证
- 数据库连接测试
- 文件和目录检查
- 端口可用性检查
- 硬编码配置检查

### 3. 配置更新工具

```bash
# 更新现有配置
node deploy-config.js
```

## 📋 配置检查清单

### 部署前检查
- [ ] `.env`文件已创建并配置
- [ ] 数据库密码已设置为强密码
- [ ] JWT_SECRET已生成随机密钥
- [ ] SERVER_HOST和PUBLIC_IP已设置为公网IP
- [ ] 数据库连接测试通过
- [ ] 端口3002可用
- [ ] 防火墙已开放必要端口

### 安全检查
- [ ] 默认密码已修改
- [ ] .env文件权限设置为600
- [ ] 数据库用户权限最小化
- [ ] SSL证书已配置（推荐）
- [ ] 防火墙规则已配置

### 功能检查
- [ ] 设备端脚本服务器地址已更新
- [ ] 前端页面可正常访问
- [ ] API接口响应正常
- [ ] WebSocket连接正常
- [ ] 设备连接测试通过

## 🔍 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u autojs_control -p -h localhost

# 检查用户权限
mysql -u root -p -e "SHOW GRANTS FOR 'autojs_control'@'%';"
```

#### 2. 设备连接失败
```bash
# 检查设备端脚本配置
grep -n "serverUrl\|SERVER_CONFIG" scripts/双向.js

# 检查防火墙
sudo ufw status
sudo netstat -tlnp | grep :3002
```

#### 3. 前端页面无法访问
```bash
# 检查Nginx配置
sudo nginx -t
sudo systemctl status nginx

# 检查静态文件
ls -la /var/www/autojs-control/
```

#### 4. JWT认证失败
```bash
# 检查JWT密钥配置
grep JWT_SECRET .env

# 重新生成JWT密钥
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### 日志查看

```bash
# PM2服务日志
pm2 logs autojs-control

# Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# 系统日志
sudo journalctl -u nginx -f
```

## 📚 高级配置

### 1. Redis缓存配置

```bash
# 安装Redis
sudo apt install redis-server

# 配置Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=你的Redis密码
REDIS_DB=0
```

### 2. 邮件通知配置

```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>
```

### 3. 负载均衡配置

```bash
# 多实例配置
# 在ecosystem.config.js中设置
instances: 'max'  # 或具体数字
exec_mode: 'cluster'
```

## 🔒 安全最佳实践

### 1. 文件权限
```bash
# 设置.env文件权限
chmod 600 .env
chown root:root .env

# 设置日志目录权限
chmod 755 logs/
chown www-data:www-data logs/
```

### 2. 数据库安全
```bash
# 运行MySQL安全脚本
sudo mysql_secure_installation

# 限制数据库访问
# 在/etc/mysql/mysql.conf.d/mysqld.cnf中添加
bind-address = 127.0.0.1
```

### 3. 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3002/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3002/tcp
sudo firewall-cmd --reload
```

## 📞 技术支持

### 配置问题
1. 运行 `node validate-config.js` 检查配置
2. 查看相关日志文件
3. 检查防火墙和网络连接
4. 参考故障排除部分

### 更新配置
1. 修改 `.env` 文件
2. 运行 `node validate-config.js` 验证
3. 重启服务：`pm2 restart autojs-control`

### 备份配置
```bash
# 备份配置文件
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)

# 备份数据库
mysqldump -u autojs_control -p autojs_control > backup_$(date +%Y%m%d_%H%M%S).sql
```

通过这套统一配置管理方案，可以彻底解决系统中的硬编码问题，实现灵活、安全、易维护的配置管理。
