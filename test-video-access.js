/**
 * 测试视频文件访问性的脚本
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

// 测试视频文件访问
async function testVideoAccess() {
  console.log('🎥 开始测试视频文件访问性...\n');

  // 1. 检查视频文件目录
  const videoDirs = [
    path.join(__dirname, 'server/uploads/videos'),
    path.join(__dirname, 'uploads/videos'),
    path.join(__dirname, 'server/core/uploads/videos')
  ];

  console.log('📁 检查视频文件目录:');
  for (const dir of videoDirs) {
    const exists = fs.existsSync(dir);
    console.log(`   ${exists ? '✅' : '❌'} ${dir}`);
    
    if (exists) {
      try {
        const files = fs.readdirSync(dir);
        console.log(`      文件数量: ${files.length}`);
        if (files.length > 0) {
          console.log(`      示例文件: ${files.slice(0, 3).join(', ')}`);
        }
      } catch (error) {
        console.log(`      读取目录失败: ${error.message}`);
      }
    }
  }

  console.log('\n🌐 测试HTTP访问:');

  // 2. 测试HTTP访问
  const testUrls = [
    'http://localhost:3002/uploads/videos/',
    'http://127.0.0.1:3002/uploads/videos/',
    'http://localhost:3000/uploads/videos/',
    'http://127.0.0.1:3000/uploads/videos/'
  ];

  for (const url of testUrls) {
    try {
      const result = await testHttpAccess(url);
      console.log(`   ${result.success ? '✅' : '❌'} ${url} - ${result.message}`);
    } catch (error) {
      console.log(`   ❌ ${url} - 错误: ${error.message}`);
    }
  }

  // 3. 如果有视频文件，测试具体文件访问
  console.log('\n🎬 测试具体视频文件访问:');
  
  let testVideoFile = null;
  for (const dir of videoDirs) {
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir).filter(f => f.endsWith('.mp4') || f.endsWith('.avi') || f.endsWith('.mov'));
      if (files.length > 0) {
        testVideoFile = files[0];
        console.log(`   找到测试视频文件: ${testVideoFile} (在 ${dir})`);
        break;
      }
    }
  }

  if (testVideoFile) {
    const testVideoUrls = [
      `http://localhost:3002/uploads/videos/${testVideoFile}`,
      `http://127.0.0.1:3002/uploads/videos/${testVideoFile}`,
      `http://localhost:3000/uploads/videos/${testVideoFile}`,
      `http://127.0.0.1:3000/uploads/videos/${testVideoFile}`
    ];

    for (const url of testVideoUrls) {
      try {
        const result = await testHttpAccess(url);
        console.log(`   ${result.success ? '✅' : '❌'} ${url} - ${result.message}`);
      } catch (error) {
        console.log(`   ❌ ${url} - 错误: ${error.message}`);
      }
    }
  } else {
    console.log('   ⚠️ 没有找到测试视频文件');
  }

  console.log('\n📋 建议:');
  console.log('1. 确保服务器正在运行 (端口3002)');
  console.log('2. 检查静态文件服务配置');
  console.log('3. 确认视频文件存在于正确的目录');
  console.log('4. 检查文件权限');
}

// 测试HTTP访问的辅助函数
function testHttpAccess(url) {
  return new Promise((resolve) => {
    const request = http.get(url, (res) => {
      resolve({
        success: res.statusCode === 200 || res.statusCode === 404, // 404也算正常响应
        message: `状态码: ${res.statusCode}`,
        statusCode: res.statusCode
      });
    });

    request.on('error', (error) => {
      resolve({
        success: false,
        message: `连接失败: ${error.message}`,
        error: error
      });
    });

    request.setTimeout(5000, () => {
      request.destroy();
      resolve({
        success: false,
        message: '请求超时',
        error: new Error('Timeout')
      });
    });
  });
}

// 检查服务器配置
function checkServerConfig() {
  console.log('\n⚙️ 检查服务器配置文件:');
  
  const configFiles = [
    'test-server.js',
    'server/server-main.js',
    'server/routes_modules/server-routes.js'
  ];

  for (const file of configFiles) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`   ✅ ${file} 存在`);
      
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查静态文件配置
        const staticConfigs = content.match(/app\.use\([^)]*static[^)]*\)/g) || [];
        console.log(`      静态文件配置数量: ${staticConfigs.length}`);
        
        staticConfigs.forEach((config, index) => {
          console.log(`      ${index + 1}. ${config}`);
        });
        
      } catch (error) {
        console.log(`      读取文件失败: ${error.message}`);
      }
    } else {
      console.log(`   ❌ ${file} 不存在`);
    }
  }
}

// 运行测试
async function runTests() {
  console.log('🚀 视频文件访问性测试开始\n');
  console.log('=' * 60);
  
  checkServerConfig();
  await testVideoAccess();
  
  console.log('\n' + '=' * 60);
  console.log('✅ 测试完成！');
}

// 如果直接运行此脚本，执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testVideoAccess,
  testHttpAccess,
  checkServerConfig,
  runTests
};
