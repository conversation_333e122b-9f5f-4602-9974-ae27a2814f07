# Auto.js云群控系统远程服务器完整部署指南

## 📋 部署概述

本指南基于系统架构分析，提供Auto.js云群控系统在远程公网服务器上的完整部署方案，支持Windows/Linux服务器，包含传统部署和宝塔面板部署两种方式。

## 🏗️ 系统架构概览

### 本地开发架构
```
本地环境:
├── 前端: Vue.js (localhost:8080) → 8,000+行代码
├── 后端: Node.js (localhost:3002) → 16,500+行代码  
├── 数据库: MySQL (localhost:3306) → 19个数据表
├── 脚本: Auto.js脚本 → 30,000+行代码
└── 设备端: 双向.js (连接localhost:3002)
```

### 远程部署架构
```
公网服务器:
├── 前端: Nginx静态服务 (公网IP:80/443)
├── 后端: PM2管理Node.js (公网IP:3002)
├── 数据库: MySQL (远程/本地数据库)
└── 设备端: Auto.js脚本 (连接公网IP:3002)
```

## 🎯 部署前准备

### 服务器要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **存储**: 50GB以上可用空间
- **网络**: 公网IP，带宽10Mbps以上
- **操作系统**: Linux (Ubuntu 18.04+/CentOS 7+) 或 Windows Server 2016+

### 🆕 统一配置管理系统
本系统已采用统一配置管理方案，解决了以下问题：
- **双数据库配置**：本地数据库(`autojs_control`) + 主站数据库(`zhuzhan`)
- **硬编码IP地址**：自动替换所有配置文件中的IP地址
- **配置分散问题**：集中在`.env`文件中管理
- **部署复杂性**：提供自动化配置工具

### 配置管理文件结构
```
├── .env                    # 主配置文件（自动生成）
├── .env.example           # 配置模板
├── config/index.js        # 统一配置模块
├── generate-config.js     # 配置生成工具
├── validate-config.js     # 配置验证工具
└── 配置管理使用说明.md    # 详细使用说明
```

## 🚀 方案一：Linux自动化部署（推荐）

### 快速部署流程

#### 第一步：使用自动化配置工具
```bash
# 1. 运行配置生成工具（交互式）
node generate-config.js

# 2. 验证配置完整性
node validate-config.js

# 3. 执行自动化部署
./deploy.sh
```

#### 第二步：验证部署结果
```bash
# 检查服务状态
pm2 status
sudo systemctl status nginx

# 访问系统
curl http://你的公网IP
```

## 🛠️ 方案二：Linux传统部署

### 第一步：服务器环境准备

#### Ubuntu/Debian系统
```bash
# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 安装Node.js 16+
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. 安装PM2进程管理器
sudo npm install -g pm2

# 4. 安装Nginx
sudo apt install nginx -y

# 5. 安装MySQL
sudo apt install mysql-server -y

# 6. 安装dotenv支持
npm install -g dotenv

# 7. 配置防火墙
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3002  # Node.js服务
sudo ufw enable
```

#### CentOS/RHEL系统
```bash
# 1. 更新系统
sudo yum update -y

# 2. 安装Node.js 16+
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
sudo yum install -y nodejs

# 3. 安装PM2
sudo npm install -g pm2

# 4. 安装Nginx
sudo yum install nginx -y

# 5. 安装MySQL
sudo yum install mysql-server -y

# 6. 配置防火墙
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3002/tcp
sudo firewall-cmd --reload
```

### 第二步：数据库配置

```bash
# 1. 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 2. 安全配置
sudo mysql_secure_installation

# 3. 创建本地数据库和用户
mysql -u root -p
```

```sql
-- 创建本地数据库（存储系统数据）
CREATE DATABASE autojs_control CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'autojs_control'@'%' IDENTIFIED BY '你的强密码';
GRANT ALL PRIVILEGES ON autojs_control.* TO 'autojs_control'@'%';

-- 如果需要主站数据库验证，创建主站数据库（可选）
CREATE DATABASE zhuzhan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- 或连接到远程主站数据库

FLUSH PRIVILEGES;
EXIT;
```

```bash
# 4. 导入数据库结构
mysql -u autojs_control -p autojs_control < 初始化数据库.sql
```

### 第三步：配置系统环境

#### 方法一：自动化配置（推荐）
```bash
# 1. 运行配置生成工具
node generate-config.js

# 按提示输入：
# - 服务器公网IP地址
# - 本地数据库密码
# - 主站数据库配置（可选）
# - 其他安全配置

# 2. 验证生成的配置
node validate-config.js
```

#### 方法二：手动配置
```bash
# 1. 复制配置模板
cp .env.example .env

# 2. 编辑配置文件
nano .env
```

编辑 `.env` 文件内容：
```bash
# 服务器配置
NODE_ENV=production
SERVER_HOST=你的公网IP
PUBLIC_IP=你的公网IP
SERVER_PORT=3002

# JWT配置
JWT_SECRET=生成的64位随机密钥

# 本地数据库配置
LOCAL_DB_HOST=localhost
LOCAL_DB_USER=autojs_control
LOCAL_DB_PASSWORD=你的强密码
LOCAL_DB_NAME=autojs_control

# 主站数据库配置（可选）
MAIN_DB_HOST=你的主站数据库IP
MAIN_DB_USER=你的主站数据库用户名
MAIN_DB_PASSWORD=你的主站数据库密码
MAIN_DB_NAME=zhuzhan
```

### 第四步：验证和应用配置

#### 1. 验证配置完整性
```bash
# 运行配置验证工具
node validate-config.js

# 验证项目包括：
# - 必需环境变量检查
# - IP地址格式验证
# - 数据库连接测试
# - 文件和目录检查
# - 端口可用性检查
# - 硬编码配置检查
```

#### 2. 自动更新配置文件
配置生成工具会自动更新以下文件：
- `config/index.js` - 统一配置模块
- `server/config/database.js` - 数据库配置
- `scripts/双向.js` - 设备端连接配置
- `ecosystem.config.js` - PM2配置
- `nginx-autojs-control.conf` - Nginx配置

#### 3. 手动检查关键配置（可选）

检查设备端脚本配置：
```bash
# 查看设备端服务器地址配置
grep -n "serverUrl\|SERVER_CONFIG" scripts/双向.js
```

检查数据库配置：
```bash
# 测试数据库连接
node -e "
const config = require('./config');
console.log('本地数据库:', config.database.local);
console.log('主站数据库:', config.database.main);
"
```

#### 4. 配置文件说明

**自动生成的配置文件**：
- `.env` - 环境变量配置文件
- `config/index.js` - 统一配置模块，从.env读取配置
- `server/config/database.js` - 双数据库连接池配置
- `scripts/双向.js` - 设备端脚本，自动更新服务器地址

**配置特点**：
- 支持本地数据库 + 主站数据库双数据库架构
- 自动替换硬编码IP地址为公网IP
- 统一的环境变量管理
- 生产环境安全配置

### 第五步：构建和部署前端

```bash
# 1. 安装项目依赖
npm install

# 2. 进入前端目录并构建
cd web
npm install
npm run build
cd ..

# 3. 创建网站目录
sudo mkdir -p /var/www/autojs-control

# 4. 复制构建文件
sudo cp -r web/dist/* /var/www/autojs-control/

# 5. 设置权限
sudo chown -R www-data:www-data /var/www/autojs-control
sudo chmod -R 755 /var/www/autojs-control
```

### 第六步：配置Nginx

创建 `/etc/nginx/sites-available/autojs-control`：
```nginx
server {
    listen 80;
    server_name 你的公网IP;  # 替换为实际的公网IP或域名
    
    # 前端静态文件
    location / {
        root /var/www/autojs-control;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理到Node.js
    location /api/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传大小限制
    client_max_body_size 100M;
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/autojs-control /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

### 第七步：部署后端服务

```bash
# 1. 安装后端依赖（如果还没安装）
npm install --production

# 2. 初始化数据库（使用新的配置系统）
node server/config/database.js

# 3. 启动后端服务
# PM2配置文件已由配置工具自动生成
pm2 start ecosystem.config.js --env production

# 4. 保存PM2配置并设置开机自启
pm2 save
pm2 startup

# 5. 验证服务状态
pm2 status
pm2 logs autojs-control
```

**注意**：
- PM2配置文件 `ecosystem.config.js` 已由配置生成工具自动创建
- 数据库配置已通过环境变量统一管理
- 不需要手动修改配置文件中的IP地址和密码

## 🖥️ 方案三：Windows服务器部署

### 第一步：Windows环境准备

```powershell
# 1. 下载并安装Node.js 16+ (https://nodejs.org/)
# 2. 安装PM2和Windows服务支持
npm install -g pm2
npm install -g pm2-windows-startup
pm2-startup install

# 3. 安装MySQL (https://dev.mysql.com/downloads/mysql/)
# 4. 配置Windows防火墙，开放端口：80, 443, 3002, 3306
```

### 第二步：Windows部署步骤

#### 方法一：使用PowerShell自动化脚本
```powershell
# 运行Windows自动化部署脚本
PowerShell -ExecutionPolicy Bypass -File quick-deploy.ps1
```

#### 方法二：手动部署
```powershell
# 1. 创建应用目录
mkdir C:\autojs-control
cd C:\autojs-control

# 2. 上传项目文件到该目录

# 3. 生成配置文件
node generate-config.js

# 4. 验证配置
node validate-config.js

# 5. 安装依赖
npm install --production

# 6. 启动服务
pm2 start ecosystem.config.js --env production
pm2 save

# 7. 配置IIS或使用Nginx for Windows作为反向代理
```

## 🎛️ 方案四：宝塔面板部署（推荐）

### 第一步：安装宝塔面板

#### Linux服务器
```bash
# CentOS安装
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec

# Ubuntu安装
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh ed8484bec
```

#### Windows服务器
1. 下载宝塔Windows面板：http://download.bt.cn/win/panel/BtSoft.zip
2. 解压到D盘根目录（D:\BtSoft\）
3. 运行BtSoft.exe安装

### 第二步：宝塔环境配置

在宝塔面板 → 软件商店安装：
- **Nginx** 1.20+
- **MySQL** 8.0
- **PM2管理器** 4.0+
- **Node.js版本管理器**（安装Node.js 16+）

### 第三步：宝塔快速部署

#### 1. 创建数据库
- 宝塔面板 → 数据库 → 添加数据库
- 数据库名：`autojs_control`
- 用户名：`autojs_control`
- 密码：设置强密码
- 导入 `初始化数据库.sql`

#### 2. 创建网站
- 宝塔面板 → 网站 → 添加站点
- 域名：你的公网IP或域名
- 根目录：`/www/wwwroot/autojs-control`

#### 3. 上传代码
- 使用宝塔文件管理器上传项目文件
- 或使用FTP上传

#### 4. 配置系统环境
```bash
cd /www/wwwroot/autojs-control

# 生成配置文件
node generate-config.js

# 验证配置
node validate-config.js
```

#### 5. 构建前端
```bash
cd web
npm install
npm run build
cd ..
```

#### 6. 配置Nginx反向代理
在网站设置 → 配置文件中添加反向代理配置（参考前面的Nginx配置）

#### 7. 启动后端服务
- 宝塔面板 → PM2管理器 → 添加项目
- 项目名称：`autojs-control`
- 启动文件：`/www/wwwroot/autojs-control/server/server-main.js`
- 运行目录：`/www/wwwroot/autojs-control`
- 环境变量：选择"从.env文件读取"或手动添加：
  ```
  NODE_ENV=production
  ```

**注意**：配置已通过.env文件统一管理，无需手动添加所有环境变量

## 🔧 统一配置管理工具

### 配置生成工具
```bash
# 交互式配置生成（推荐）
node generate-config.js

# 配置验证
node validate-config.js

# 快速部署（Linux）
./deploy.sh
```

### 配置工具功能

#### 1. generate-config.js
- **功能**：交互式生成完整配置
- **生成文件**：
  - `.env` - 环境变量配置
  - `config/index.js` - 统一配置模块
  - `ecosystem.config.js` - PM2配置
  - `nginx-autojs-control.conf` - Nginx配置
  - `deploy.sh` - 部署脚本

#### 2. validate-config.js
- **功能**：验证配置完整性和正确性
- **验证项目**：
  - 必需环境变量检查
  - IP地址格式验证
  - 数据库连接测试
  - 文件和目录检查
  - 端口可用性检查
  - 硬编码配置检查

#### 3. 配置特点
- **双数据库支持**：本地数据库 + 主站数据库
- **自动IP替换**：替换所有硬编码IP地址
- **安全配置**：自动生成强密码和JWT密钥
- **向后兼容**：保持与现有代码的兼容性

## ✅ 部署验证和测试

### 第一步：使用配置验证工具

```bash
# 1. 运行配置验证工具（推荐）
node validate-config.js

# 该工具会自动检查：
# - 环境变量配置
# - 数据库连接
# - 服务端口状态
# - 文件权限
# - 硬编码配置
```

### 第二步：手动服务状态检查

```bash
# 1. 检查PM2服务状态
pm2 status
pm2 logs autojs-control

# 2. 检查Nginx状态
sudo systemctl status nginx
sudo nginx -t

# 3. 检查数据库连接（双数据库）
# 本地数据库
mysql -u autojs_control -p autojs_control
# 主站数据库（如果配置了）
mysql -u 主站用户名 -p -h 主站IP zhuzhan

# 4. 检查端口监听
netstat -tlnp | grep :3002
netstat -tlnp | grep :80

# 5. 检查防火墙状态
sudo ufw status  # Ubuntu
sudo firewall-cmd --list-all  # CentOS
```

### 第三步：功能测试

#### 1. 前端访问测试
```bash
# 访问前端页面
curl -I http://你的公网IP

# 应该返回200状态码
```

#### 2. API接口测试
```bash
# 测试设备列表API
curl http://你的公网IP:3002/api/devices

# 测试健康检查
curl http://你的公网IP:3002/api/health

# 测试配置API
curl http://你的公网IP:3002/api/config
```

#### 3. WebSocket连接测试
- 在浏览器中访问 `http://你的公网IP`
- 登录系统（默认：admin/admin123）
- 查看设备管理页面，确认WebSocket连接正常

#### 4. 设备连接测试
- 在Android设备上运行自动更新后的 `scripts/双向.js`
- 确认设备能成功连接到服务器
- 在Web界面查看设备状态

#### 5. 数据库连接测试
```bash
# 测试本地数据库
node -e "
const config = require('./config');
const mysql = require('mysql2/promise');
(async () => {
  try {
    const conn = await mysql.createConnection(config.database.local);
    console.log('本地数据库连接成功');
    await conn.end();
  } catch (err) {
    console.error('本地数据库连接失败:', err.message);
  }
})();
"

# 测试主站数据库（如果配置了）
node -e "
const config = require('./config');
const mysql = require('mysql2/promise');
(async () => {
  try {
    const conn = await mysql.createConnection(config.database.main);
    console.log('主站数据库连接成功');
    await conn.end();
  } catch (err) {
    console.error('主站数据库连接失败:', err.message);
  }
})();
"
```

### 第三步：性能测试

```bash
# 1. 压力测试（可选）
# 安装Apache Bench
sudo apt install apache2-utils

# 测试API性能
ab -n 1000 -c 10 http://你的公网IP:3002/api/devices

# 2. 监控系统资源
htop
df -h
free -h
```

## 🔒 安全配置和加固

### 第一步：SSL证书配置

#### 使用Let's Encrypt免费证书
```bash
# 1. 安装certbot
sudo apt install certbot python3-certbot-nginx -y

# 2. 获取证书（需要域名）
sudo certbot --nginx -d 你的域名

# 3. 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

#### 手动配置SSL
如果使用IP访问，可以创建自签名证书：
```bash
# 创建自签名证书
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/autojs.key \
  -out /etc/ssl/certs/autojs.crt

# 更新Nginx配置支持HTTPS
```

### 第二步：防火墙配置

```bash
# Ubuntu/Debian
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3002/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --remove-service=ssh
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3002/tcp
sudo firewall-cmd --reload
```

### 第三步：数据库安全

```bash
# 1. 运行MySQL安全脚本
sudo mysql_secure_installation

# 2. 限制数据库访问
# 编辑 /etc/mysql/mysql.conf.d/mysqld.cnf
# 添加：bind-address = 127.0.0.1

# 3. 重启MySQL
sudo systemctl restart mysql
```

### 第四步：应用安全

#### 1. 修改默认密码
- 登录系统后立即修改admin账户密码
- 创建新的管理员账户，删除默认账户

#### 2. 配置访问限制
```javascript
// 在server/middleware/auth.js中添加IP白名单
const allowedIPs = ['你的IP地址'];

function ipWhitelist(req, res, next) {
  const clientIP = req.ip || req.connection.remoteAddress;
  if (allowedIPs.includes(clientIP)) {
    next();
  } else {
    res.status(403).json({ error: 'Access denied' });
  }
}
```

## 📊 监控和维护

### 第一步：日志管理

```bash
# 1. 配置日志轮转
sudo nano /etc/logrotate.d/autojs-control

# 内容：
/var/log/autojs-control*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        pm2 reload autojs-control
    endscript
}

# 2. 查看日志
pm2 logs autojs-control
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 第二步：性能监控

```bash
# 1. 安装监控工具
sudo apt install htop iotop nethogs

# 2. PM2监控
pm2 monit

# 3. 系统监控脚本
cat > monitor.sh << EOF
#!/bin/bash
echo "=== 系统状态监控 ==="
echo "时间: $(date)"
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)"
echo "内存使用: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "磁盘使用: $(df -h / | tail -1 | awk '{print $5}')"
echo "网络连接: $(netstat -an | grep :3002 | wc -l)"
echo "========================"
EOF

chmod +x monitor.sh
```

### 第三步：备份策略

```bash
# 1. 数据库备份脚本
cat > backup-db.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/autojs-control"
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u autojs_control -p你的数据库密码 autojs_control > $BACKUP_DIR/db_$DATE.sql

# 备份应用文件
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /opt/autojs-control

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "备份完成: $DATE"
EOF

chmod +x backup-db.sh

# 2. 设置定时备份
crontab -e
# 添加：0 2 * * * /path/to/backup-db.sh
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 配置问题诊断
```bash
# 首先运行配置验证工具
node validate-config.js

# 检查环境变量
cat .env

# 检查配置加载
node -e "console.log(require('./config'))"
```

#### 2. Node.js服务启动失败
```bash
# 检查错误日志
pm2 logs autojs-control

# 常见原因：
# - 端口被占用：sudo lsof -i :3002
# - 权限问题：sudo chown -R $USER:$USER /opt/autojs-control
# - 依赖缺失：npm install
# - 环境变量缺失：检查 .env 文件
# - 配置错误：运行 node validate-config.js
```

#### 3. 数据库连接失败
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 测试本地数据库连接
mysql -u autojs_control -p -h localhost autojs_control

# 测试主站数据库连接（如果配置）
mysql -u 主站用户名 -p -h 主站IP zhuzhan

# 使用配置验证工具测试
node validate-config.js

# 常见原因：
# - 用户权限：重新授权数据库用户
# - 防火墙：检查3306端口
# - 配置错误：检查 .env 文件中的数据库配置
# - 密码错误：确认 .env 中的密码正确
```

#### 3. 前端页面无法访问
```bash
# 检查Nginx状态
sudo systemctl status nginx
sudo nginx -t

# 检查文件权限
ls -la /var/www/autojs-control/

# 常见原因：
# - Nginx配置错误：检查配置文件语法
# - 文件权限：sudo chown -R www-data:www-data /var/www/autojs-control
# - 防火墙：检查80/443端口
```

#### 4. WebSocket连接失败
```bash
# 检查Nginx WebSocket配置
# 确保包含以下配置：
# proxy_set_header Upgrade $http_upgrade;
# proxy_set_header Connection "upgrade";

# 检查防火墙WebSocket端口
sudo ufw allow 3002/tcp
```

#### 5. 设备连接失败
```bash
# 检查设备端配置是否自动更新
grep -n "serverUrl\|SERVER_CONFIG" scripts/双向.js

# 手动验证设备端配置
node -e "
const fs = require('fs');
const content = fs.readFileSync('scripts/双向.js', 'utf8');
const match = content.match(/serverUrl.*?=.*?[\"'](.*?)[\"']/);
console.log('设备端服务器地址:', match ? match[1] : '未找到');
"

# 检查服务器防火墙
sudo ufw status

# 检查网络连通性
ping 你的公网IP
telnet 你的公网IP 3002

# 常见原因：
# - 设备端IP地址未更新：重新运行 node generate-config.js
# - 防火墙阻止：检查端口3002是否开放
# - 网络问题：确认公网IP可访问
```

#### 6. 配置文件问题
```bash
# 检查 .env 文件是否存在
ls -la .env

# 检查 .env 文件权限
chmod 600 .env

# 重新生成配置
node generate-config.js

# 验证配置
node validate-config.js
```

### 紧急恢复流程

```bash
# 1. 停止所有服务
pm2 stop all
sudo systemctl stop nginx

# 2. 恢复备份
# 恢复数据库
mysql -u autojs_control -p autojs_control < /backup/autojs-control/db_最新.sql

# 恢复应用文件
tar -xzf /backup/autojs-control/app_最新.tar.gz -C /

# 3. 重启服务
sudo systemctl start nginx
pm2 start ecosystem.config.js --env production
```

## 📋 部署检查清单

### 服务器环境准备
- [ ] 服务器系统更新完成
- [ ] Node.js 16+ 安装完成
- [ ] PM2 进程管理器安装完成
- [ ] Nginx Web服务器安装完成
- [ ] MySQL 数据库安装完成
- [ ] dotenv 支持安装完成
- [ ] 防火墙端口配置完成（80, 443, 3002）

### 🆕 统一配置管理
- [ ] 运行 `node generate-config.js` 生成配置
- [ ] `.env` 文件创建并配置完成
- [ ] 运行 `node validate-config.js` 验证通过
- [ ] 配置文件自动更新完成

### 数据库配置
- [ ] MySQL 服务启动并设置开机自启
- [ ] 本地数据库 `autojs_control` 创建完成
- [ ] 本地数据库用户 `autojs_control` 创建并授权
- [ ] 主站数据库 `zhuzhan` 配置完成（可选）
- [ ] 数据库表结构导入完成（初始化数据库.sql）
- [ ] 双数据库连接测试通过

### 配置验证
- [ ] 必需环境变量检查通过
- [ ] IP地址格式验证通过
- [ ] 数据库连接测试通过
- [ ] 文件和目录检查通过
- [ ] 端口可用性检查通过
- [ ] 硬编码配置检查通过

### 前端部署
- [ ] 前端依赖安装完成（npm install）
- [ ] 前端项目构建完成（npm run build）
- [ ] 静态文件部署到 /var/www/autojs-control
- [ ] 文件权限设置正确
- [ ] Nginx 配置文件自动生成并启用
- [ ] Nginx 反向代理配置正确
- [ ] Nginx 配置测试通过（nginx -t）

### 后端部署
- [ ] 后端代码上传到服务器
- [ ] 后端依赖安装完成（npm install --production）
- [ ] PM2 配置文件自动生成（ecosystem.config.js）
- [ ] 环境变量通过 .env 文件配置
- [ ] 数据库初始化完成
- [ ] PM2 服务启动成功
- [ ] PM2 开机自启配置完成

### 功能验证
- [ ] 前端页面访问正常（http://公网IP）
- [ ] API 接口响应正常（http://公网IP:3002/api/devices）
- [ ] 配置API响应正常（http://公网IP:3002/api/config）
- [ ] 用户登录功能正常
- [ ] WebSocket 连接正常
- [ ] 设备连接测试通过（自动更新IP地址）
- [ ] 本地数据库连接正常
- [ ] 主站数据库连接正常（如果配置）
- [ ] 小红书自动化功能测试通过
- [ ] 闲鱼自动化功能测试通过

### 安全配置
- [ ] SSL 证书配置完成（可选）
- [ ] 防火墙规则配置正确
- [ ] 数据库安全配置完成
- [ ] JWT密钥已生成强随机值
- [ ] 默认密码修改完成
- [ ] .env文件权限设置为600
- [ ] 访问日志配置完成

### 监控和维护
- [ ] 日志轮转配置完成
- [ ] 性能监控工具安装
- [ ] 备份策略制定并测试
- [ ] 监控脚本部署完成
- [ ] 告警机制配置完成
- [ ] 配置文件备份完成

## 🎯 部署后优化建议

### 性能优化
1. **启用Gzip压缩**
```nginx
# 在Nginx配置中添加
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

2. **配置缓存策略**
```nginx
# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

3. **数据库优化**
```sql
-- 添加索引优化查询性能
ALTER TABLE devices ADD INDEX idx_user_id (user_id);
ALTER TABLE execution_logs ADD INDEX idx_created_at (created_at);
```

### 扩展性配置
1. **负载均衡配置**（多服务器部署）
2. **Redis缓存集成**（提高性能）
3. **CDN配置**（加速静态资源）
4. **数据库读写分离**（高并发场景）

### 运维自动化
1. **CI/CD流水线**（自动化部署）
2. **容器化部署**（Docker + Kubernetes）
3. **监控告警系统**（Prometheus + Grafana）
4. **日志收集系统**（ELK Stack）

## 📞 技术支持和联系方式

### 常见问题解决
1. **查看系统文档**：参考项目中的其他文档文件
2. **检查日志文件**：PM2日志、Nginx日志、系统日志
3. **社区支持**：GitHub Issues、技术论坛
4. **专业支持**：联系开发团队

### 更新和维护
1. **定期更新**：系统补丁、依赖包更新
2. **安全扫描**：定期进行安全漏洞扫描
3. **性能监控**：持续监控系统性能指标
4. **备份验证**：定期验证备份文件完整性

---

## 🎉 部署完成

恭喜！你已经成功完成了Auto.js云群控系统的远程服务器部署。

### 系统访问信息
- **前端地址**：http://你的公网IP
- **API地址**：http://你的公网IP:3002
- **配置API**：http://你的公网IP:3002/api/config
- **默认账户**：admin / admin123（请立即修改）

### 🆕 配置管理特性
- **双数据库支持**：本地数据库 + 主站数据库
- **统一配置管理**：通过 .env 文件集中管理
- **自动IP替换**：无需手动修改硬编码地址
- **配置验证**：自动检查配置完整性和正确性
- **安全增强**：自动生成强密码和JWT密钥

### 下一步操作
1. 运行 `node validate-config.js` 验证部署
2. 登录系统并修改默认密码
3. 检查设备端脚本配置是否正确
4. 测试双数据库连接（如果配置了主站数据库）
5. 配置监控和告警
6. 制定备份和恢复计划

### 重要提醒
- **配置安全**：.env 文件权限设置为 600
- **定期备份**：备份数据库和配置文件
- **监控系统**：监控系统资源使用情况
- **及时更新**：及时更新系统和依赖包
- **配置验证**：定期运行配置验证工具

### 配置管理工具
```bash
# 生成新配置
node generate-config.js

# 验证现有配置
node validate-config.js

# 查看配置详情
node -e "console.log(JSON.stringify(require('./config'), null, 2))"
```

**🚀 享受你的Auto.js云群控系统吧！现在配置管理更简单、更安全！**
```
