<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频预览测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .url-test {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .url-test input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .url-test button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .url-test button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
            border-radius: 8px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 视频预览测试</h1>
        
        <div class="test-section">
            <h3>1. 环境信息</h3>
            <div id="env-info"></div>
        </div>

        <div class="test-section">
            <h3>2. 自动测试已知视频文件</h3>
            <button onclick="testKnownVideos()">测试已知视频文件</button>
            <div id="known-videos-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 手动测试视频URL</h3>
            <div class="url-test">
                <input type="text" id="manual-url" placeholder="输入视频URL，例如: /uploads/videos/video-123.mp4">
                <button onclick="testManualUrl()">测试此URL</button>
            </div>
            <div id="manual-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试日志</h3>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script>
        let logContent = '';

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent += `[${timestamp}] ${message}\n`;
            document.getElementById('test-log').textContent = logContent;
            console.log(message);
        }

        function showEnvironmentInfo() {
            const info = {
                hostname: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                fullUrl: window.location.href,
                userAgent: navigator.userAgent
            };

            document.getElementById('env-info').innerHTML = `
                <div><strong>主机名:</strong> ${info.hostname}</div>
                <div><strong>端口:</strong> ${info.port}</div>
                <div><strong>协议:</strong> ${info.protocol}</div>
                <div><strong>完整URL:</strong> ${info.fullUrl}</div>
                <div><strong>用户代理:</strong> ${info.userAgent}</div>
            `;

            log(`环境信息: ${JSON.stringify(info, null, 2)}`);
        }

        async function testKnownVideos() {
            log('开始测试已知视频文件...');
            
            const knownVideos = [
                'video-1755062344993-738168247.mp4', // 从测试结果中获取的实际文件名
                'video-1754461531076-852891644.mp4',
                'video-1754463297648-914915794.mp4'
            ];

            const baseUrls = [
                '/uploads/videos/',
                'http://localhost:3002/uploads/videos/',
                'http://127.0.0.1:3002/uploads/videos/'
            ];

            const resultDiv = document.getElementById('known-videos-result');
            resultDiv.innerHTML = '';

            for (const video of knownVideos) {
                for (const baseUrl of baseUrls) {
                    const fullUrl = baseUrl + video;
                    log(`测试URL: ${fullUrl}`);
                    
                    const result = await testVideoUrl(fullUrl);
                    
                    const resultElement = document.createElement('div');
                    resultElement.className = `result ${result.success ? 'success' : 'error'}`;
                    resultElement.innerHTML = `
                        <strong>${fullUrl}</strong><br>
                        状态: ${result.success ? '✅ 成功' : '❌ 失败'}<br>
                        消息: ${result.message}
                        ${result.success ? `<br><video controls src="${fullUrl}">您的浏览器不支持视频播放</video>` : ''}
                    `;
                    resultDiv.appendChild(resultElement);
                }
            }
        }

        async function testManualUrl() {
            const url = document.getElementById('manual-url').value.trim();
            if (!url) {
                alert('请输入视频URL');
                return;
            }

            log(`手动测试URL: ${url}`);
            
            const result = await testVideoUrl(url);
            
            const resultDiv = document.getElementById('manual-result');
            resultDiv.innerHTML = `
                <div class="result ${result.success ? 'success' : 'error'}">
                    <strong>${url}</strong><br>
                    状态: ${result.success ? '✅ 成功' : '❌ 失败'}<br>
                    消息: ${result.message}
                    ${result.success ? `<br><video controls src="${url}">您的浏览器不支持视频播放</video>` : ''}
                </div>
            `;
        }

        async function testVideoUrl(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                
                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    const contentLength = response.headers.get('content-length');
                    
                    log(`✅ ${url} - 状态码: ${response.status}, 类型: ${contentType}, 大小: ${contentLength}`);
                    
                    return {
                        success: true,
                        message: `状态码: ${response.status}, 类型: ${contentType}, 大小: ${contentLength || '未知'}`
                    };
                } else {
                    log(`❌ ${url} - 状态码: ${response.status}`);
                    return {
                        success: false,
                        message: `HTTP错误: ${response.status} ${response.statusText}`
                    };
                }
            } catch (error) {
                log(`❌ ${url} - 错误: ${error.message}`);
                return {
                    success: false,
                    message: `网络错误: ${error.message}`
                };
            }
        }

        // 页面加载时显示环境信息
        window.onload = function() {
            showEnvironmentInfo();
            log('页面加载完成，准备进行视频预览测试');
        };
    </script>
</body>
</html>
