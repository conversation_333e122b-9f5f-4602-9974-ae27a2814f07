#!/usr/bin/env node

/**
 * Auto.js云群控系统配置验证工具
 * 验证配置文件的完整性和正确性
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 加载环境变量
function loadEnvConfig() {
  if (!fs.existsSync('.env')) {
    log('❌ .env 文件不存在', 'red');
    log('请先运行 node generate-config.js 生成配置文件', 'yellow');
    return null;
  }

  require('dotenv').config();
  return process.env;
}

// 验证必需的环境变量
function validateRequiredEnvVars(env) {
  log('🔍 验证必需的环境变量...', 'cyan');
  
  const required = [
    'SERVER_HOST',
    'PUBLIC_IP',
    'LOCAL_DB_PASSWORD',
    'JWT_SECRET'
  ];
  
  const missing = [];
  const weak = [];
  
  required.forEach(key => {
    if (!env[key]) {
      missing.push(key);
    } else if (key === 'JWT_SECRET' && env[key].length < 32) {
      weak.push(key);
    } else if (key.includes('PASSWORD') && env[key] === 'root') {
      weak.push(key);
    }
  });
  
  if (missing.length > 0) {
    log(`❌ 缺少必需的环境变量: ${missing.join(', ')}`, 'red');
    return false;
  }
  
  if (weak.length > 0) {
    log(`⚠️ 以下配置项使用了弱密码或默认值: ${weak.join(', ')}`, 'yellow');
    log('建议在生产环境中使用更强的密码', 'yellow');
  }
  
  log('✅ 必需的环境变量验证通过', 'green');
  return true;
}

// 验证IP地址格式
function validateIPAddress(env) {
  log('🔍 验证IP地址格式...', 'cyan');
  
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  const ips = ['SERVER_HOST', 'PUBLIC_IP'];
  let valid = true;
  
  ips.forEach(key => {
    if (env[key] && env[key] !== 'localhost' && !ipRegex.test(env[key])) {
      log(`❌ ${key} 不是有效的IP地址格式: ${env[key]}`, 'red');
      valid = false;
    }
  });
  
  if (valid) {
    log('✅ IP地址格式验证通过', 'green');
  }
  
  return valid;
}

// 测试数据库连接
async function testDatabaseConnection(env) {
  log('🔍 测试数据库连接...', 'cyan');
  
  // 测试本地数据库
  const localConfig = {
    host: env.LOCAL_DB_HOST || 'localhost',
    port: parseInt(env.LOCAL_DB_PORT) || 3306,
    user: env.LOCAL_DB_USER || 'autojs_control',
    password: env.LOCAL_DB_PASSWORD,
    database: env.LOCAL_DB_NAME || 'autojs_control'
  };
  
  try {
    const localConnection = await mysql.createConnection(localConfig);
    await localConnection.ping();
    await localConnection.end();
    log('✅ 本地数据库连接成功', 'green');
  } catch (error) {
    log(`❌ 本地数据库连接失败: ${error.message}`, 'red');
    log('请检查数据库配置和服务状态', 'yellow');
    return false;
  }
  
  // 测试主站数据库（可选）
  if (env.MAIN_DB_HOST && env.MAIN_DB_HOST !== 'localhost') {
    const mainConfig = {
      host: env.MAIN_DB_HOST,
      port: parseInt(env.MAIN_DB_PORT) || 3306,
      user: env.MAIN_DB_USER || 'root',
      password: env.MAIN_DB_PASSWORD,
      database: env.MAIN_DB_NAME || 'zhuzhan'
    };
    
    try {
      const mainConnection = await mysql.createConnection(mainConfig);
      await mainConnection.ping();
      await mainConnection.end();
      log('✅ 主站数据库连接成功', 'green');
    } catch (error) {
      log(`⚠️ 主站数据库连接失败: ${error.message}`, 'yellow');
      log('如果不需要主站数据库验证，这是正常的', 'yellow');
    }
  }
  
  return true;
}

// 验证文件和目录
function validateFilesAndDirectories(env) {
  log('🔍 验证文件和目录...', 'cyan');
  
  const checks = [
    { path: 'server/server-main.js', type: 'file', name: '主服务器文件' },
    { path: 'web/dist', type: 'directory', name: '前端构建目录', optional: true },
    { path: 'scripts/双向.js', type: 'file', name: '设备端脚本' },
    { path: env.UPLOAD_PATH || './uploads', type: 'directory', name: '上传目录', create: true },
    { path: path.dirname(env.LOG_FILE_PATH || './logs/app.log'), type: 'directory', name: '日志目录', create: true }
  ];
  
  let allValid = true;
  
  checks.forEach(check => {
    const exists = fs.existsSync(check.path);
    
    if (!exists) {
      if (check.optional) {
        log(`⚠️ ${check.name} 不存在: ${check.path} (可选)`, 'yellow');
      } else if (check.create) {
        try {
          fs.mkdirSync(check.path, { recursive: true });
          log(`✅ 已创建 ${check.name}: ${check.path}`, 'green');
        } catch (error) {
          log(`❌ 无法创建 ${check.name}: ${check.path}`, 'red');
          allValid = false;
        }
      } else {
        log(`❌ ${check.name} 不存在: ${check.path}`, 'red');
        allValid = false;
      }
    } else {
      log(`✅ ${check.name} 存在: ${check.path}`, 'green');
    }
  });
  
  return allValid;
}

// 验证端口可用性
async function validatePorts(env) {
  log('🔍 验证端口配置...', 'cyan');
  
  const net = require('net');
  const port = parseInt(env.SERVER_PORT) || 3002;
  
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, () => {
      server.close(() => {
        log(`✅ 端口 ${port} 可用`, 'green');
        resolve(true);
      });
    });
    
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        log(`⚠️ 端口 ${port} 已被占用`, 'yellow');
        log('请确保没有其他服务使用此端口', 'yellow');
      } else {
        log(`❌ 端口 ${port} 检查失败: ${err.message}`, 'red');
      }
      resolve(false);
    });
  });
}

// 检查硬编码配置
function checkHardcodedConfigs() {
  log('🔍 检查硬编码配置...', 'cyan');
  
  const filesToCheck = [
    'scripts/双向.js',
    'web/src/utils/serverConfig.js',
    'server/config/database.js'
  ];
  
  let foundIssues = false;
  
  filesToCheck.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查常见的硬编码IP
      const hardcodedIPs = ['************', '127.0.0.1', 'localhost'];
      const foundHardcoded = hardcodedIPs.filter(ip => 
        content.includes(ip) && !content.includes(`// ${ip}`) // 排除注释
      );
      
      if (foundHardcoded.length > 0) {
        log(`⚠️ ${filePath} 中发现硬编码IP: ${foundHardcoded.join(', ')}`, 'yellow');
        foundIssues = true;
      } else {
        log(`✅ ${filePath} 配置检查通过`, 'green');
      }
    }
  });
  
  if (!foundIssues) {
    log('✅ 未发现硬编码配置问题', 'green');
  }
  
  return !foundIssues;
}

// 生成配置报告
function generateConfigReport(env) {
  log('\n📋 配置报告', 'blue');
  console.log('='.repeat(50));
  
  console.log(`服务器地址: ${env.SERVER_HOST}:${env.SERVER_PORT}`);
  console.log(`公网IP: ${env.PUBLIC_IP}`);
  console.log(`环境: ${env.NODE_ENV}`);
  console.log(`本地数据库: ${env.LOCAL_DB_USER}@${env.LOCAL_DB_HOST}:${env.LOCAL_DB_PORT}/${env.LOCAL_DB_NAME}`);
  console.log(`主站数据库: ${env.MAIN_DB_USER}@${env.MAIN_DB_HOST}:${env.MAIN_DB_PORT}/${env.MAIN_DB_NAME}`);
  console.log(`上传目录: ${env.UPLOAD_PATH}`);
  console.log(`日志文件: ${env.LOG_FILE_PATH}`);
  console.log(`JWT过期时间: ${env.JWT_EXPIRES_IN}`);
  console.log(`最大设备数: ${env.MAX_DEVICES_PER_USER}`);
  console.log(`允许注册: ${env.ENABLE_REGISTRATION}`);
  console.log(`维护模式: ${env.MAINTENANCE_MODE}`);
  
  console.log('='.repeat(50));
}

// 主验证函数
async function validateConfig() {
  log('=== Auto.js云群控系统配置验证工具 ===', 'blue');
  console.log();
  
  // 加载配置
  const env = loadEnvConfig();
  if (!env) {
    process.exit(1);
  }
  
  let allValid = true;
  
  // 执行各项验证
  allValid &= validateRequiredEnvVars(env);
  allValid &= validateIPAddress(env);
  allValid &= validateFilesAndDirectories(env);
  allValid &= await validatePorts(env);
  allValid &= await testDatabaseConnection(env);
  allValid &= checkHardcodedConfigs();
  
  console.log();
  
  if (allValid) {
    log('🎉 所有配置验证通过！', 'green');
    generateConfigReport(env);
    
    console.log();
    log('下一步操作：', 'cyan');
    log('1. 运行 npm install 安装依赖');
    log('2. 运行 cd web && npm run build 构建前端');
    log('3. 运行 pm2 start ecosystem.config.js --env production 启动服务');
  } else {
    log('❌ 配置验证失败，请修复上述问题后重试', 'red');
    process.exit(1);
  }
}

// 执行验证
if (require.main === module) {
  validateConfig().catch(error => {
    log(`验证过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { validateConfig };
