# =====================================================
# Auto.js云群控系统 - 环境配置文件
# 复制此文件为 .env 并根据您的环境修改配置
# =====================================================

# 服务器配置
NODE_ENV=production
SERVER_PORT=3002
SERVER_HOST=你的公网IP
PUBLIC_IP=你的公网IP

# JWT配置 - 生产环境必须修改
JWT_SECRET=请使用generate-config.js生成或手动设置64位随机字符串
JWT_EXPIRES_IN=24h

# 本地数据库配置（现有系统数据）
LOCAL_DB_HOST=localhost
LOCAL_DB_PORT=3306
LOCAL_DB_USER=autojs_control
LOCAL_DB_PASSWORD=请设置强密码
LOCAL_DB_NAME=autojs_control

# 主站数据库配置（用于账号验证，可选）
MAIN_DB_HOST=localhost
MAIN_DB_PORT=3306
MAIN_DB_USER=root
MAIN_DB_PASSWORD=请设置强密码
MAIN_DB_NAME=zhuzhan

# 数据库连接池配置
DB_CONNECTION_LIMIT=10
DB_QUEUE_LIMIT=0
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# 系统配置
DEFAULT_TRIAL_DAYS=7
MAX_DEVICES_PER_USER=50
ENABLE_REGISTRATION=false
MAINTENANCE_MODE=false

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# 文件上传配置
UPLOAD_MAX_SIZE=104857600
UPLOAD_PATH=./uploads

# 缓存配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=

# 安全配置
BCRYPT_ROUNDS=10
SESSION_TIMEOUT=86400
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# WebSocket配置
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000
WS_MAX_HTTP_BUFFER_SIZE=100000000

# 设备连接配置
DEVICE_RECONNECTION_COOLDOWN=10000
DEVICE_MAX_RECONNECTION_ATTEMPTS=5
DEVICE_RECONNECTION_DELAY=2000

# =====================================================
# 配置说明
# =====================================================

# SERVER_HOST: 服务器公网IP地址，设备端将连接此地址
# PUBLIC_IP: 公网IP地址，用于前端配置和设备连接

# LOCAL_DB_*: 本地数据库配置，存储系统数据（用户、设备、日志等）
# MAIN_DB_*: 主站数据库配置，用于账号验证（可选）

# JWT_SECRET: JWT令牌加密密钥，生产环境必须使用强随机字符串
# 可以使用以下命令生成：
# node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# ENABLE_REGISTRATION: 是否允许用户注册
# MAINTENANCE_MODE: 维护模式，开启后系统将显示维护页面

# 文件权限建议：
# chmod 600 .env  # 仅所有者可读写
# chown root:root .env  # 设置为root用户所有

# 安全提醒：
# 1. 生产环境必须修改所有默认密码
# 2. JWT_SECRET必须使用强随机字符串
# 3. 数据库密码必须使用强密码
# 4. 定期更新系统和依赖包
# 5. 配置防火墙和SSL证书
