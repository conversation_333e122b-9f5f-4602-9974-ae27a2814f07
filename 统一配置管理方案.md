# Auto.js云群控系统统一配置管理方案

## 📋 问题分析

通过代码分析发现系统中存在以下配置问题：

### 🔍 数据库配置问题
1. **双数据库系统**：
   - 本地数据库：`autojs_control` (用于系统数据)
   - 主站数据库：`zhuzhan` (用于账号验证)

2. **硬编码配置**：
   - `server/config/database.js` 中硬编码了数据库账号密码
   - 本地数据库：`user: 'autojs_control', password: 'root'`
   - 主站数据库：`user: 'zhuzhan', password: 'root'`

### 🌐 IP地址硬编码问题
1. **设备端脚本**：
   - `scripts/双向.js` 中默认服务器地址：`************:3002`
   - 需要手动修改为公网IP

2. **Nginx配置**：
   - 反向代理地址：`127.0.0.1:3002`
   - 需要根据实际部署调整

3. **前端配置**：
   - 开发代理配置中的localhost需要替换

## 🎯 统一配置管理解决方案

### 方案一：环境变量配置 (.env文件)

创建完整的 `.env` 配置文件：

```bash
# =====================================================
# Auto.js云群控系统 - 生产环境配置
# =====================================================

# 服务器配置
NODE_ENV=production
SERVER_PORT=3002
SERVER_HOST=你的公网IP
PUBLIC_IP=你的公网IP

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-very-long-and-random

# 本地数据库配置（现有系统）
LOCAL_DB_HOST=localhost
LOCAL_DB_PORT=3306
LOCAL_DB_USER=autojs_control
LOCAL_DB_PASSWORD=你的本地数据库密码
LOCAL_DB_NAME=autojs_control

# 主站数据库配置（用于账号验证）
MAIN_DB_HOST=你的主站数据库IP
MAIN_DB_PORT=3306
MAIN_DB_USER=你的主站数据库用户名
MAIN_DB_PASSWORD=你的主站数据库密码
MAIN_DB_NAME=zhuzhan

# 数据库连接池配置
DB_CONNECTION_LIMIT=10
DB_QUEUE_LIMIT=0
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# 系统配置
DEFAULT_TRIAL_DAYS=7
MAX_DEVICES_PER_USER=50
ENABLE_REGISTRATION=false
MAINTENANCE_MODE=false

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# 文件上传配置
UPLOAD_MAX_SIZE=104857600
UPLOAD_PATH=./uploads

# 缓存配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=

# 安全配置
BCRYPT_ROUNDS=10
SESSION_TIMEOUT=86400
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# WebSocket配置
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000
WS_MAX_HTTP_BUFFER_SIZE=100000000

# 设备连接配置
DEVICE_RECONNECTION_COOLDOWN=10000
DEVICE_MAX_RECONNECTION_ATTEMPTS=5
DEVICE_RECONNECTION_DELAY=2000
```

### 方案二：统一配置模块

创建 `config/index.js` 统一配置管理：

```javascript
const path = require('path');
const fs = require('fs');

// 加载环境变量
require('dotenv').config();

// 配置验证函数
function validateConfig() {
  const required = [
    'SERVER_HOST',
    'LOCAL_DB_PASSWORD',
    'JWT_SECRET'
  ];
  
  const missing = required.filter(key => !process.env[key]);
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }
}

// 统一配置对象
const config = {
  // 环境配置
  env: process.env.NODE_ENV || 'development',
  
  // 服务器配置
  server: {
    host: process.env.SERVER_HOST || 'localhost',
    port: parseInt(process.env.SERVER_PORT) || 3002,
    publicIP: process.env.PUBLIC_IP || process.env.SERVER_HOST || 'localhost'
  },
  
  // 数据库配置
  database: {
    local: {
      host: process.env.LOCAL_DB_HOST || 'localhost',
      port: parseInt(process.env.LOCAL_DB_PORT) || 3306,
      user: process.env.LOCAL_DB_USER || 'autojs_control',
      password: process.env.LOCAL_DB_PASSWORD || 'root',
      database: process.env.LOCAL_DB_NAME || 'autojs_control',
      charset: 'utf8mb4',
      timezone: '+08:00',
      connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
      queueLimit: parseInt(process.env.DB_QUEUE_LIMIT) || 0,
      acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
      timeout: parseInt(process.env.DB_TIMEOUT) || 60000
    },
    
    main: {
      host: process.env.MAIN_DB_HOST || 'localhost',
      port: parseInt(process.env.MAIN_DB_PORT) || 3306,
      user: process.env.MAIN_DB_USER || 'root',
      password: process.env.MAIN_DB_PASSWORD || 'root',
      database: process.env.MAIN_DB_NAME || 'zhuzhan',
      charset: 'utf8mb4',
      timezone: '+08:00',
      connectionLimit: 5,
      queueLimit: 0,
      acquireTimeout: 60000,
      timeout: 60000
    }
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },
  
  // 系统配置
  system: {
    defaultTrialDays: parseInt(process.env.DEFAULT_TRIAL_DAYS) || 7,
    maxDevicesPerUser: parseInt(process.env.MAX_DEVICES_PER_USER) || 50,
    enableRegistration: process.env.ENABLE_REGISTRATION === 'true',
    maintenanceMode: process.env.MAINTENANCE_MODE === 'true'
  },
  
  // 文件上传配置
  upload: {
    maxSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 104857600, // 100MB
    path: process.env.UPLOAD_PATH || './uploads'
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/app.log'
  },
  
  // WebSocket配置
  websocket: {
    pingTimeout: parseInt(process.env.WS_PING_TIMEOUT) || 60000,
    pingInterval: parseInt(process.env.WS_PING_INTERVAL) || 25000,
    maxHttpBufferSize: parseInt(process.env.WS_MAX_HTTP_BUFFER_SIZE) || 100000000,
    cors: {
      origin: "*",
      methods: ["GET", "POST"],
      credentials: true
    }
  },
  
  // 设备连接配置
  device: {
    reconnectionCooldown: parseInt(process.env.DEVICE_RECONNECTION_COOLDOWN) || 10000,
    maxReconnectionAttempts: parseInt(process.env.DEVICE_MAX_RECONNECTION_ATTEMPTS) || 5,
    reconnectionDelay: parseInt(process.env.DEVICE_RECONNECTION_DELAY) || 2000
  },
  
  // 安全配置
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 10,
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT) || 86400,
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5,
    lockoutDuration: parseInt(process.env.LOCKOUT_DURATION) || 900
  },
  
  // Redis配置（可选）
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0
  },
  
  // 邮件配置（可选）
  email: {
    host: process.env.SMTP_HOST || '',
    port: parseInt(process.env.SMTP_PORT) || 587,
    user: process.env.SMTP_USER || '',
    password: process.env.SMTP_PASSWORD || '',
    from: process.env.SMTP_FROM || ''
  }
};

// 开发环境时验证配置
if (config.env === 'production') {
  validateConfig();
}

module.exports = config;
```

### 方案三：更新数据库配置文件

修改 `server/config/database.js`：

```javascript
const mysql = require('mysql2/promise');
const config = require('../../config');

// 本地数据库配置
const localDbConfig = {
  host: config.database.local.host,
  port: config.database.local.port,
  user: config.database.local.user,
  password: config.database.local.password,
  database: config.database.local.database,
  charset: config.database.local.charset,
  timezone: config.database.local.timezone
};

// 主站数据库配置
const mainDbConfig = {
  host: config.database.main.host,
  port: config.database.main.port,
  user: config.database.main.user,
  password: config.database.main.password,
  database: config.database.main.database,
  charset: config.database.main.charset,
  timezone: config.database.main.timezone
};

// 创建本地数据库连接池
const localPool = mysql.createPool({
  ...localDbConfig,
  waitForConnections: true,
  connectionLimit: config.database.local.connectionLimit,
  queueLimit: config.database.local.queueLimit,
  acquireTimeout: config.database.local.acquireTimeout,
  timeout: config.database.local.timeout,
  reconnect: true
});

// 创建主站数据库连接池
const mainPool = mysql.createPool({
  ...mainDbConfig,
  waitForConnections: true,
  connectionLimit: config.database.main.connectionLimit,
  queueLimit: config.database.main.queueLimit,
  acquireTimeout: config.database.main.acquireTimeout,
  timeout: config.database.main.timeout,
  reconnect: true
});

// 数据库初始化函数
async function initDatabase() {
  try {
    console.log('🔧 初始化数据库...');
    
    const dbConnection = await localPool.getConnection();
    
    // 这里包含所有的数据库初始化SQL...
    // (保持原有的初始化逻辑)
    
    dbConnection.release();
    console.log('✅ 数据库初始化完成');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  }
}

// 数据库连接测试函数
async function testDatabaseConnections() {
  console.log('🔍 测试数据库连接...');

  try {
    const localConnection = await localPool.getConnection();
    console.log('✅ 本地数据库连接成功');
    localConnection.release();
  } catch (error) {
    console.error('❌ 本地数据库连接失败:', error.message);
  }

  try {
    const mainConnection = await mainPool.getConnection();
    console.log('✅ 主站数据库连接成功');
    mainConnection.release();
  } catch (error) {
    console.error('❌ 主站数据库连接失败:', error.message);
    console.log('💡 提示：如果主站数据库未配置，这是正常的。系统将使用本地认证模式。');
  }
}

// 导出数据库连接池和配置
module.exports = {
  // 向后兼容
  pool: localPool,
  initDatabase,
  dbConfig: localDbConfig,

  // 新的双数据库支持
  localPool,
  mainPool,

  // 配置信息
  localDbConfig,
  mainDbConfig,

  // 工具函数
  testDatabaseConnections
};
```

## 🔧 自动化配置工具

### 配置生成脚本

创建 `generate-config.js`：

```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// 生成随机JWT密钥
function generateJWTSecret() {
  return crypto.randomBytes(64).toString('hex');
}

// 生成强密码
function generatePassword(length = 16) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

async function generateConfig() {
  console.log('=== Auto.js云群控系统配置生成工具 ===\n');

  // 基础配置
  const publicIP = await question('请输入服务器公网IP地址: ');
  const environment = await question('请选择环境 (development/production) [production]: ') || 'production';

  console.log('\n--- 本地数据库配置 ---');
  const localDbHost = await question('本地数据库主机 [localhost]: ') || 'localhost';
  const localDbUser = await question('本地数据库用户名 [autojs_control]: ') || 'autojs_control';
  const localDbPassword = await question('本地数据库密码: ');
  const localDbName = await question('本地数据库名 [autojs_control]: ') || 'autojs_control';

  console.log('\n--- 主站数据库配置（可选，用于账号验证）---');
  const useMainDb = await question('是否配置主站数据库？(y/N): ');
  let mainDbHost = 'localhost';
  let mainDbUser = 'root';
  let mainDbPassword = 'root';
  let mainDbName = 'zhuzhan';

  if (useMainDb.toLowerCase() === 'y') {
    mainDbHost = await question('主站数据库主机: ');
    mainDbUser = await question('主站数据库用户名: ');
    mainDbPassword = await question('主站数据库密码: ');
    mainDbName = await question('主站数据库名 [zhuzhan]: ') || 'zhuzhan';
  }

  console.log('\n--- 安全配置 ---');
  const jwtSecret = generateJWTSecret();
  console.log(`JWT密钥已自动生成: ${jwtSecret.substring(0, 20)}...`);

  // 生成.env文件内容
  const envContent = `# =====================================================
# Auto.js云群控系统 - 环境配置文件
# 自动生成于 ${new Date().toISOString()}
# =====================================================

# 服务器配置
NODE_ENV=${environment}
SERVER_PORT=3002
SERVER_HOST=${publicIP}
PUBLIC_IP=${publicIP}

# JWT配置
JWT_SECRET=${jwtSecret}

# 本地数据库配置（现有系统）
LOCAL_DB_HOST=${localDbHost}
LOCAL_DB_PORT=3306
LOCAL_DB_USER=${localDbUser}
LOCAL_DB_PASSWORD=${localDbPassword}
LOCAL_DB_NAME=${localDbName}

# 主站数据库配置（用于账号验证）
MAIN_DB_HOST=${mainDbHost}
MAIN_DB_PORT=3306
MAIN_DB_USER=${mainDbUser}
MAIN_DB_PASSWORD=${mainDbPassword}
MAIN_DB_NAME=${mainDbName}

# 数据库连接池配置
DB_CONNECTION_LIMIT=10
DB_QUEUE_LIMIT=0
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# 系统配置
DEFAULT_TRIAL_DAYS=7
MAX_DEVICES_PER_USER=50
ENABLE_REGISTRATION=false
MAINTENANCE_MODE=false

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# 文件上传配置
UPLOAD_MAX_SIZE=104857600
UPLOAD_PATH=./uploads

# 缓存配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=

# 安全配置
BCRYPT_ROUNDS=10
SESSION_TIMEOUT=86400
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# WebSocket配置
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000
WS_MAX_HTTP_BUFFER_SIZE=100000000

# 设备连接配置
DEVICE_RECONNECTION_COOLDOWN=10000
DEVICE_MAX_RECONNECTION_ATTEMPTS=5
DEVICE_RECONNECTION_DELAY=2000
`;

  // 写入.env文件
  fs.writeFileSync('.env', envContent);
  console.log('\n✅ .env 配置文件已生成');

  // 更新设备端脚本
  updateDeviceScript(publicIP);

  // 生成PM2配置
  generatePM2Config(publicIP);

  // 生成Nginx配置
  generateNginxConfig(publicIP);

  console.log('\n=== 配置生成完成 ===');
  console.log(`服务器地址: http://${publicIP}`);
  console.log(`API地址: http://${publicIP}:3002`);
  console.log('\n下一步：');
  console.log('1. 检查并调整 .env 文件中的配置');
  console.log('2. 运行 npm install 安装依赖');
  console.log('3. 运行 node server/config/database.js 初始化数据库');
  console.log('4. 运行 pm2 start ecosystem.config.js --env production 启动服务');

  rl.close();
}

function updateDeviceScript(publicIP) {
  const scriptPath = 'scripts/双向.js';
  if (fs.existsSync(scriptPath)) {
    let content = fs.readFileSync(scriptPath, 'utf8');

    // 替换服务器地址
    const serverUrlPattern = /text=".*?:\d+"/g;
    content = content.replace(serverUrlPattern, `text="${publicIP}:3002"`);

    // 添加配置块
    const configBlock = `// 服务器配置 - 自动生成
var SERVER_CONFIG = {
    host: "${publicIP}",
    port: 3002,
    url: "http://${publicIP}:3002"
};

`;

    if (!content.includes('SERVER_CONFIG')) {
      content = configBlock + content;
    }

    fs.writeFileSync(scriptPath, content);
    console.log('✅ 设备端脚本配置已更新');
  }
}

function generatePM2Config(publicIP) {
  const pm2Config = `module.exports = {
  apps: [{
    name: 'autojs-control',
    script: 'server/server-main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production'
    },
    error_file: '/var/log/autojs-control-error.log',
    out_file: '/var/log/autojs-control-out.log',
    log_file: '/var/log/autojs-control.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    restart_delay: 4000
  }]
};`;

  fs.writeFileSync('ecosystem.config.js', pm2Config);
  console.log('✅ PM2配置文件已生成');
}

function generateNginxConfig(publicIP) {
  const nginxConfig = `server {
    listen 80;
    server_name ${publicIP};

    # 前端静态文件
    location / {
        root /var/www/autojs-control;
        index index.html;
        try_files $uri $uri/ /index.html;

        # 缓存静态资源
        location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理到Node.js
    location /api/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件上传大小限制
    client_max_body_size 100M;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}`;

  fs.writeFileSync('nginx-autojs-control.conf', nginxConfig);
  console.log('✅ Nginx配置文件已生成');
}

if (require.main === module) {
  generateConfig().catch(console.error);
}

module.exports = { generateConfig };
```

## 🚀 使用方法

### 1. 生成配置文件
```bash
# 运行配置生成工具
node generate-config.js

# 或者使用之前的工具
node deploy-config.js
```

### 2. 手动配置.env文件
```bash
# 复制示例文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 3. 验证配置
```bash
# 测试数据库连接
node -e "require('./config').database && console.log('配置加载成功')"

# 测试数据库连接
node server/config/database.js
```

## 📋 配置检查清单

### 必需配置项
- [ ] `SERVER_HOST` - 服务器公网IP
- [ ] `PUBLIC_IP` - 公网IP地址
- [ ] `LOCAL_DB_PASSWORD` - 本地数据库密码
- [ ] `JWT_SECRET` - JWT密钥（生产环境必须修改）

### 可选配置项
- [ ] `MAIN_DB_HOST` - 主站数据库地址
- [ ] `MAIN_DB_USER` - 主站数据库用户名
- [ ] `MAIN_DB_PASSWORD` - 主站数据库密码
- [ ] `REDIS_HOST` - Redis缓存地址
- [ ] `SMTP_HOST` - 邮件服务器地址

### 硬编码地址检查
- [ ] `scripts/双向.js` - 设备端服务器地址
- [ ] Nginx配置文件 - 反向代理地址
- [ ] PM2配置文件 - 环境变量设置

## 🔒 安全建议

1. **生产环境必须修改的配置**：
   - JWT_SECRET：使用强随机密钥
   - 数据库密码：使用强密码
   - 默认管理员密码：立即修改

2. **网络安全**：
   - 配置防火墙规则
   - 使用SSL证书
   - 限制数据库访问IP

3. **文件权限**：
   - .env文件权限设置为600
   - 日志文件目录权限设置正确
   - 上传目录权限控制

通过这个统一配置管理方案，可以彻底解决系统中的硬编码问题，实现灵活的环境配置管理。
