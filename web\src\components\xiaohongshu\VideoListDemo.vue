<template>
  <div class="video-list-demo">
    <el-card>
      <div slot="header">
        <span>Element UI 视频组件演示</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshDemo">刷新演示</el-button>
      </div>
      
      <!-- 演示说明 -->
      <el-alert
        title="功能演示"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;"
      >
        <div>
          • <strong>内嵌播放</strong>：点击播放按钮直接在卡片中播放视频<br>
          • <strong>Element UI 美化</strong>：使用 Card、Tag、Button 等组件美化界面<br>
          • <strong>视频控制</strong>：支持暂停、关闭、全屏预览等操作<br>
          • <strong>响应式布局</strong>：自适应网格布局，支持多种屏幕尺寸
        </div>
      </el-alert>

      <!-- 模拟视频列表 -->
      <div class="demo-video-grid">
        <el-card
          v-for="video in demoVideos"
          :key="video.id"
          class="demo-video-card"
          :body-style="{ padding: '0px' }"
          shadow="hover"
        >
          <!-- 视频预览区域 -->
          <div class="demo-video-preview">
            <!-- 缩略图模式 -->
            <div 
              v-if="!video.isPlaying" 
              class="demo-video-thumbnail" 
              @click="playVideo(video)"
            >
              <div class="demo-thumbnail-placeholder">
                <i class="el-icon-video-camera"></i>
                <span>{{ video.name }}</span>
              </div>

              <!-- 时长标签 -->
              <div class="demo-duration-badge">
                {{ video.duration }}
              </div>

              <!-- 播放按钮 -->
              <div class="demo-play-overlay">
                <el-button 
                  type="primary" 
                  icon="el-icon-video-play" 
                  circle 
                  size="large"
                  class="demo-play-button"
                ></el-button>
              </div>
            </div>

            <!-- 播放状态 -->
            <div v-else class="demo-video-player">
              <div class="demo-player-placeholder">
                <i class="el-icon-loading"></i>
                <span>{{ video.name }} 正在播放...</span>
              </div>
              
              <!-- 视频控制按钮 -->
              <div class="demo-video-controls">
                <el-button-group>
                  <el-button 
                    size="mini" 
                    icon="el-icon-video-pause"
                    @click="pauseVideo(video)"
                  >
                    暂停
                  </el-button>
                  <el-button 
                    size="mini" 
                    icon="el-icon-close"
                    @click="closeVideo(video)"
                  >
                    关闭
                  </el-button>
                  <el-button 
                    size="mini" 
                    icon="el-icon-full-screen"
                    @click="fullScreen(video)"
                  >
                    全屏
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </div>

          <!-- 视频信息 -->
          <div class="demo-video-info" style="padding: 12px;">
            <div class="demo-video-title">
              <el-tooltip :content="video.name" placement="top">
                <span>{{ video.name }}</span>
              </el-tooltip>
            </div>
            
            <div class="demo-video-meta">
              <el-tag size="mini" type="info">{{ video.size }}</el-tag>
              <el-tag size="mini" type="success">{{ video.format }}</el-tag>
              <el-tag size="mini" type="warning">{{ video.resolution }}</el-tag>
            </div>
            
            <!-- 统计信息 -->
            <div class="demo-video-stats">
              <el-row :gutter="8">
                <el-col :span="12">
                  <div class="demo-stat-item">
                    <i class="el-icon-time"></i>
                    <span>{{ video.uploadTime }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="demo-stat-item">
                    <i class="el-icon-data-analysis"></i>
                    <span>播放: {{ video.playCount }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="demo-video-actions" style="padding: 8px 12px; border-top: 1px solid #f0f0f0;">
            <el-button-group>
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-view"
                @click="previewVideo(video)"
              >
                全屏预览
              </el-button>
              <el-button
                size="mini"
                type="info"
                icon="el-icon-download"
                @click="downloadVideo(video)"
              >
                下载
              </el-button>
              <el-button
                size="mini"
                type="success"
                icon="el-icon-share"
                @click="shareVideo(video)"
              >
                分享
              </el-button>
            </el-button-group>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'VideoListDemo',
  data() {
    return {
      demoVideos: [
        {
          id: 1,
          name: '小红书营销视频_01.mp4',
          size: '25.6MB',
          format: 'MP4',
          resolution: '1080P',
          duration: '02:35',
          uploadTime: '2024-01-15',
          playCount: 128,
          isPlaying: false
        },
        {
          id: 2,
          name: '产品展示视频_demo.mov',
          size: '45.2MB',
          format: 'MOV',
          resolution: '4K',
          duration: '01:42',
          uploadTime: '2024-01-14',
          playCount: 89,
          isPlaying: false
        },
        {
          id: 3,
          name: '用户评价合集_final.avi',
          size: '18.9MB',
          format: 'AVI',
          resolution: '720P',
          duration: '03:21',
          uploadTime: '2024-01-13',
          playCount: 256,
          isPlaying: false
        },
        {
          id: 4,
          name: '品牌宣传片_2024.mkv',
          size: '67.8MB',
          format: 'MKV',
          resolution: '1080P',
          duration: '04:15',
          uploadTime: '2024-01-12',
          playCount: 342,
          isPlaying: false
        }
      ]
    }
  },
  methods: {
    playVideo(video) {
      // 先暂停所有其他视频
      this.demoVideos.forEach(v => {
        if (v.id !== video.id) {
          v.isPlaying = false
        }
      })
      
      // 播放当前视频
      video.isPlaying = true
      this.$message.success(`开始播放: ${video.name}`)
      
      // 模拟播放结束
      setTimeout(() => {
        if (video.isPlaying) {
          video.isPlaying = false
          this.$message.info(`播放完成: ${video.name}`)
        }
      }, 5000)
    },
    
    pauseVideo(video) {
      this.$message.info(`暂停播放: ${video.name}`)
    },
    
    closeVideo(video) {
      video.isPlaying = false
      this.$message.success(`关闭播放: ${video.name}`)
    },
    
    fullScreen(video) {
      this.$message.info(`全屏播放: ${video.name}`)
    },
    
    previewVideo(video) {
      this.$message.info(`全屏预览: ${video.name}`)
    },
    
    downloadVideo(video) {
      this.$message.success(`开始下载: ${video.name}`)
    },
    
    shareVideo(video) {
      this.$message.success(`分享视频: ${video.name}`)
    },
    
    refreshDemo() {
      // 重置所有视频状态
      this.demoVideos.forEach(video => {
        video.isPlaying = false
      })
      this.$message.success('演示已刷新')
    }
  }
}
</script>

<style scoped>
.video-list-demo {
  padding: 20px;
}

.demo-video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.demo-video-card {
  transition: all 0.3s;
}

.demo-video-preview {
  position: relative;
  width: 100%;
  height: 200px;
  background: #f5f7fa;
}

.demo-video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: transform 0.2s;
  
  &:hover {
    transform: scale(1.02);
    
    .demo-play-overlay {
      opacity: 1;
    }
  }
}

.demo-thumbnail-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  i {
    font-size: 32px;
    margin-bottom: 8px;
  }
  
  span {
    font-size: 14px;
    text-align: center;
    padding: 0 10px;
  }
}

.demo-duration-badge {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.demo-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s;

  .demo-play-button {
    width: 50px;
    height: 50px;
    font-size: 20px;
    background: rgba(64, 158, 255, 0.9);
    border: none;
    
    &:hover {
      background: rgba(64, 158, 255, 1);
      transform: scale(1.1);
    }
  }
}

.demo-video-player {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  
  .demo-player-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: white;
    
    i {
      font-size: 32px;
      margin-bottom: 8px;
      animation: rotating 2s linear infinite;
    }
  }
  
  .demo-video-controls {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 4px;
  }
}

.demo-video-info {
  .demo-video-title {
    font-weight: 500;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    color: #303133;
  }

  .demo-video-meta {
    margin-bottom: 8px;
    
    .el-tag {
      margin-right: 4px;
    }
  }

  .demo-video-stats {
    .demo-stat-item {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #606266;
      
      i {
        margin-right: 4px;
        color: #909399;
      }
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
