<template>
  <el-dialog
    title="视频文件管理"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose"
    class="video-list-dialog"
  >
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          size="small"
          @click="loadVideoFiles"
          :loading="loading"
        >
          刷新列表
        </el-button>
        <el-button
          type="success"
          icon="el-icon-upload"
          size="small"
          @click="showUploadDialog"
        >
          上传视频
        </el-button>

        <!-- 批量操作按钮 -->
        <el-button
          type="info"
          icon="el-icon-check"
          @click="toggleSelectAll"
          size="small"
        >
          {{ isAllSelected ? '取消全选' : '全选' }}
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="batchDeleteVideos"
          :disabled="selectedVideoIds.length === 0"
          size="small"
        >
          批量删除 ({{ selectedVideoIds.length }})
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-delete-solid"
          @click="clearAllVideos"
          size="small"
        >
          清空所有
        </el-button>

        <!-- 传输视频按钮 -->
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="transferVideosToDevice"
          :disabled="selectedVideoIds.length === 0"
          size="small"
        >
          传输视频 ({{ selectedVideoIds.length }})
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索视频文件名"
          size="small"
          style="width: 250px;"
          @input="filterVideoFiles"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
    </div>

    <!-- 视频列表 -->
    <div class="video-grid" v-loading="loading">
      <!-- 使用Element UI Card组件增强视频卡片 -->
      <el-card
        v-for="video in filteredVideoFiles"
        :key="video.id"
        class="video-card"
        :class="{ 'selected': isVideoSelected(video.id) }"
        :body-style="{ padding: '0px' }"
        shadow="hover"
      >
        <!-- 选择框 -->
        <div class="selection-checkbox">
          <el-checkbox
            :value="isVideoSelected(video.id)"
            @change="toggleVideoSelection(video.id)"
            @click.stop
          ></el-checkbox>
        </div>

        <!-- 视频预览区域 -->
        <div class="video-preview-container">
          <!-- 缩略图模式 -->
          <div
            v-if="!video.isPlaying"
            class="video-thumbnail"
            @click="toggleVideoPlay(video)"
          >
            <img
              v-if="video.thumbnail_path"
              :src="getThumbnailUrl(video.thumbnail_path)"
              :alt="video.original_name"
              @error="handleImageError"
            />
            <div v-else class="no-thumbnail">
              <i class="el-icon-video-camera"></i>
              <span>无预览</span>
            </div>

            <!-- 视频时长 -->
            <div v-if="video.video_duration > 0" class="duration-badge">
              {{ formatDuration(video.video_duration) }}
            </div>

            <!-- 播放按钮 -->
            <div class="play-overlay">
              <el-button
                type="primary"
                icon="el-icon-video-play"
                circle
                size="large"
                class="play-button"
              ></el-button>
            </div>
          </div>

          <!-- 内嵌视频播放器 -->
          <div v-else class="video-player-container">
            <video
              :ref="`player_${video.id}`"
              :src="getVideoUrl(video.file_path)"
              controls
              preload="metadata"
              class="inline-video-player"
              @ended="onVideoEnded(video)"
              @pause="onVideoPaused(video)"
            >
              您的浏览器不支持视频播放
            </video>

            <!-- 视频控制按钮 -->
            <div class="video-controls">
              <el-button-group>
                <el-button
                  size="mini"
                  icon="el-icon-video-pause"
                  @click="pauseVideo(video)"
                >
                  暂停
                </el-button>
                <el-button
                  size="mini"
                  icon="el-icon-close"
                  @click="closeVideo(video)"
                >
                  关闭
                </el-button>
                <el-button
                  size="mini"
                  icon="el-icon-full-screen"
                  @click="openFullPreview(video)"
                >
                  全屏
                </el-button>
              </el-button-group>
            </div>
          </div>
        </div>

        <!-- 视频信息 -->
        <div class="video-info" style="padding: 12px;">
          <div class="video-title" :title="video.original_name">
            <el-tooltip :content="video.original_name" placement="top">
              <span>{{ video.original_name }}</span>
            </el-tooltip>
          </div>
          <div class="video-meta">
            <el-tag size="mini" type="info">{{ formatFileSize(video.file_size) }}</el-tag>
            <el-tag size="mini" type="success">{{ video.video_format.toUpperCase() }}</el-tag>
            <el-tag v-if="video.video_resolution" size="mini" type="warning">{{ video.video_resolution }}</el-tag>
          </div>

          <!-- 使用Element UI组件美化统计信息 -->
          <div class="video-stats">
            <el-row :gutter="8">
              <el-col :span="12">
                <div class="stat-item">
                  <i class="el-icon-time"></i>
                  <span>{{ formatTime(video.upload_time) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <i class="el-icon-data-analysis"></i>
                  <span>分配: {{ video.assignment_count || 0 }} | 完成: {{ video.completed_count || 0 }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="transfer-stats">
            <el-row :gutter="8">
              <el-col :span="8">
                <div class="stat-item">
                  <i class="el-icon-download"></i>
                  <span>{{ video.total_transfer_count || video.transfer_count || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <i class="el-icon-check"></i>
                  <span>{{ video.successful_transfer_count || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="8" v-if="video.last_transfer_time">
                <div class="stat-item">
                  <i class="el-icon-refresh"></i>
                  <span>{{ formatTime(video.last_transfer_time) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <div v-if="video.transferred_devices && getTransferredDevices(video.transferred_devices).length > 0" class="transferred-devices">
            <div class="devices-label">
              <i class="el-icon-mobile-phone"></i>
              已传输设备:
            </div>
            <el-tag
              v-for="device in getTransferredDevices(video.transferred_devices)"
              :key="device.deviceId"
              size="mini"
              type="success"
              :title="`设备: ${device.deviceName} (${device.ipAddress}), 传输时间: ${formatTime(device.transferTime)}`"
              style="margin: 2px;"
            >
              {{ device.ipAddress || device.deviceName }}
            </el-tag>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="video-actions" style="padding: 8px 12px; border-top: 1px solid #f0f0f0;">
          <el-button-group>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click.stop="openFullPreview(video)"
            >
              全屏预览
            </el-button>
            <el-button
              size="mini"
              type="info"
              icon="el-icon-data-line"
              @click.stop="showTransferInfo(video)"
            >
              传输信息
            </el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click.stop="deleteVideo(video)"
            >
              删除
            </el-button>
          </el-button-group>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="pagination.total > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[12, 24, 48, 96]"
        :page-size="pagination.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      >
      </el-pagination>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      title="上传视频文件"
      :visible.sync="uploadDialogVisible"
      width="600px"
      :append-to-body="true"
    >
      <div class="upload-section">
        <div class="upload-buttons">
          <!-- 文件上传 -->
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            accept="video/*"
            :show-file-list="true"
            :disabled="uploading"
            multiple
            name="videos"
          >
            <el-button size="small" type="primary" icon="el-icon-upload" :loading="uploading">
              {{ uploading ? '上传中...' : '选择视频文件' }}
            </el-button>
          </el-upload>

          <!-- 文件夹上传 -->
          <div class="folder-upload">
            <input
              ref="folderInput"
              type="file"
              webkitdirectory
              directory
              multiple
              accept="video/*"
              style="display: none"
              @change="handleFolderSelect"
              :disabled="uploading"
            />
            <el-button
              size="small"
              type="success"
              icon="el-icon-folder-add"
              @click="selectFolder"
              :disabled="uploading"
            >
              选择视频文件夹
            </el-button>
          </div>
        </div>

        <div class="upload-tips">
          <div>支持 MP4、AVI、MOV、WMV、FLV、MKV 等格式，单个文件不超过2GB</div>
          <div style="color: #E6A23C; margin-top: 5px;">
            ⚠️ 可选择文件夹批量上传（最多1000个文件），系统会自动检测重复文件
          </div>
        </div>

        <!-- 上传进度 -->
        <div v-if="uploading" class="upload-progress">
          <el-progress :percentage="uploadProgress" :status="uploadStatus"></el-progress>
          <p>{{ uploadMessage }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 视频预览对话框 -->
    <el-dialog
      title="视频预览"
      :visible.sync="previewDialogVisible"
      width="70%"
      :append-to-body="true"
    >
      <div v-if="currentPreviewVideo" class="video-preview">
        <div class="preview-video">
          <video
            ref="previewPlayer"
            :src="getVideoUrl(currentPreviewVideo.file_path)"
            controls
            preload="metadata"
            style="width: 100%; max-height: 400px;"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
        <div class="preview-info">
          <h3>{{ currentPreviewVideo.original_name }}</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>文件大小:</label>
              <span>{{ formatFileSize(currentPreviewVideo.file_size) }}</span>
            </div>
            <div class="info-item">
              <label>视频格式:</label>
              <span>{{ currentPreviewVideo.video_format.toUpperCase() }}</span>
            </div>
            <div class="info-item">
              <label>视频时长:</label>
              <span>{{ formatDuration(currentPreviewVideo.video_duration) }}</span>
            </div>
            <div class="info-item">
              <label>分辨率:</label>
              <span>{{ currentPreviewVideo.video_resolution || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>上传时间:</label>
              <span>{{ formatTime(currentPreviewVideo.upload_time) }}</span>
            </div>
            <div class="info-item">
              <label>使用统计:</label>
              <span>分配 {{ currentPreviewVideo.assignment_count || 0 }} 次，完成 {{ currentPreviewVideo.completed_count || 0 }} 次</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import axios from 'axios'

export default {
  name: 'VideoListDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      videoFiles: [],
      filteredVideoFiles: [],
      searchKeyword: '',
      loading: false,
      uploading: false,
      uploadProgress: 0,
      uploadStatus: '',
      uploadMessage: '',
      uploadDialogVisible: false,
      previewDialogVisible: false,
      currentPreviewVideo: null,
      selectedVideoIds: [],
      pagination: {
        page: 1,
        limit: 24,
        total: 0,
        totalPages: 0
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    uploadUrl() {
      // 使用动态服务器地址
      const { getApiBaseUrl } = require('@/utils/serverConfig')
      return `${getApiBaseUrl()}/api/xiaohongshu/upload-video-files`
    },
    uploadHeaders() {
      const token = this.$store.getters['auth/token']
      return token ? { 'Authorization': `Bearer ${token}` } : {}
    },

    isAllSelected() {
      return this.filteredVideoFiles.length > 0 &&
             this.selectedVideoIds.length === this.filteredVideoFiles.length
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadVideoFiles()
      }
    }
  },
  methods: {
    async loadVideoFiles() {
      this.loading = true
      try {
        const response = await axios.get('/api/xiaohongshu/video-files', {
          params: {
            page: this.pagination.page,
            limit: this.pagination.limit,
            status: 'active'
          }
        })

        if (response.data.success) {
          this.videoFiles = response.data.data.videos
          this.pagination = response.data.data.pagination
          console.log('📹 VideoListDialog: 加载的视频数据:', this.videoFiles)
          console.log('📊 VideoListDialog: 第一个视频的传输数据:', this.videoFiles[0])
          this.filterVideoFiles()
        }
      } catch (error) {
        console.error('加载视频文件列表失败:', error)
        this.$message.error('加载视频文件列表失败')
      } finally {
        this.loading = false
      }
    },

    filterVideoFiles() {
      if (!this.searchKeyword.trim()) {
        this.filteredVideoFiles = this.videoFiles
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredVideoFiles = this.videoFiles.filter(video =>
          video.original_name.toLowerCase().includes(keyword)
        )
      }
    },

    showUploadDialog() {
      this.uploadDialogVisible = true
    },

    beforeUpload(file) {
      const isVideo = file.type.startsWith('video/') || this.isVideoFile(file.name)
      if (!isVideo) {
        this.$message.error('只能上传视频文件!')
        return false
      }

      const isLt2G = file.size / 1024 / 1024 / 1024 < 2
      if (!isLt2G) {
        this.$message.error(`视频文件 "${file.name}" 大小不能超过 2GB! 当前大小: ${this.formatFileSize(file.size)}`)
        return false
      }

      this.uploading = true
      this.uploadProgress = 0
      this.uploadStatus = ''
      this.uploadMessage = '准备上传...'
      return true
    },

    // 检查是否为视频文件
    isVideoFile(fileName) {
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp']
      const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      return videoExtensions.includes(ext)
    },

    handleUploadSuccess(response, file) {
      this.uploading = false
      if (response.success) {
        let message = response.message
        if (response.data.duplicateCount > 0) {
          message += `，其中 ${response.data.duplicateCount} 个重复文件已跳过`
        }
        this.$message.success(message)
        this.uploadDialogVisible = false
        this.loadVideoFiles()
        this.$emit('videos-uploaded', response.data)
      } else {
        this.$message.error(response.message || '上传失败')
      }
    },

    handleUploadError(error) {
      this.uploading = false
      this.$message.error('上传失败: ' + error.message)
    },

    // 选择文件夹
    selectFolder() {
      this.$refs.folderInput.click()
    },

    // 处理文件夹选择
    async handleFolderSelect(event) {
      const files = Array.from(event.target.files)
      if (files.length === 0) {
        return
      }

      // 过滤出视频文件并检查大小
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp']
      const videoFiles = []
      const oversizedFiles = []

      files.forEach(file => {
        const ext = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
        if (videoExtensions.includes(ext)) {
          const fileSizeGB = file.size / 1024 / 1024 / 1024
          if (fileSizeGB < 2) {
            videoFiles.push(file)
          } else {
            oversizedFiles.push({
              name: file.name,
              size: this.formatFileSize(file.size)
            })
          }
        }
      })

      // 提示超大文件
      if (oversizedFiles.length > 0) {
        const oversizedNames = oversizedFiles.map(f => `${f.name} (${f.size})`).join(', ')
        this.$message.warning(`以下文件超过2GB限制，已跳过: ${oversizedNames}`)
      }

      if (videoFiles.length === 0) {
        this.$message.warning('选择的文件夹中没有找到视频文件')
        return
      }

      // 获取文件夹名称
      const firstFile = files[0]
      const pathParts = firstFile.webkitRelativePath.split('/')
      const folderName = pathParts[0]

      this.$message.info(`正在上传文件夹 "${folderName}" 中的 ${videoFiles.length} 个视频文件...`)

      // 直接上传文件夹中的视频
      this.uploading = true
      this.uploadProgress = 0
      this.uploadMessage = `正在上传文件夹 "${folderName}" 中的视频...`

      try {
        const formData = new FormData()

        // 添加所有视频文件
        videoFiles.forEach(file => {
          formData.append('videos', file)
        })

        // 添加描述信息
        formData.append('description', `从文件夹 "${folderName}" 批量上传`)
        formData.append('tags', `文件夹上传,${folderName}`)

        const response = await axios.post(this.uploadUrl, formData, {
          headers: {
            ...this.uploadHeaders
            // 不要手动设置Content-Type，让axios自动设置
          },
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          }
        })

        this.handleUploadSuccess(response.data)

        // 清空文件选择
        this.$refs.folderInput.value = ''

      } catch (error) {
        this.handleUploadError(error)
      }
    },

    // 切换视频播放状态（内嵌播放）
    toggleVideoPlay(video) {
      // 先暂停所有其他正在播放的视频
      this.pauseAllVideos()

      // 设置当前视频为播放状态
      this.$set(video, 'isPlaying', true)

      // 等待DOM更新后开始播放
      this.$nextTick(() => {
        const player = this.$refs[`player_${video.id}`]
        if (player && player[0]) {
          player[0].load()
          player[0].play().catch(error => {
            console.error('视频播放失败:', error)
            this.$message.error('视频播放失败，请检查文件是否完整')
            this.$set(video, 'isPlaying', false)
          })
        }
      })
    },

    // 暂停视频
    pauseVideo(video) {
      const player = this.$refs[`player_${video.id}`]
      if (player && player[0]) {
        player[0].pause()
      }
    },

    // 关闭视频播放
    closeVideo(video) {
      const player = this.$refs[`player_${video.id}`]
      if (player && player[0]) {
        player[0].pause()
        player[0].currentTime = 0
      }
      this.$set(video, 'isPlaying', false)
    },

    // 暂停所有视频
    pauseAllVideos() {
      this.filteredVideoFiles.forEach(video => {
        if (video.isPlaying) {
          this.closeVideo(video)
        }
      })
    },

    // 视频播放结束事件
    onVideoEnded(video) {
      this.$set(video, 'isPlaying', false)
      this.$message.success(`视频 "${video.original_name}" 播放完成`)
    },

    // 视频暂停事件
    onVideoPaused(video) {
      // 可以在这里添加暂停时的逻辑
    },

    // 打开全屏预览（原有的预览功能）
    openFullPreview(video) {
      this.previewVideo(video)
    },

    previewVideo(video) {
      this.currentPreviewVideo = video
      this.previewDialogVisible = true

      // 等待对话框打开后再加载视频
      this.$nextTick(() => {
        if (this.$refs.previewPlayer) {
          this.$refs.previewPlayer.load()
        }
      })
    },

    // 显示传输信息
    showTransferInfo(video) {
      console.log('🎬🎬🎬 VideoListDialog: ===== 显示传输信息被调用 ===== 🎬🎬🎬')
      console.log('🎬 VideoListDialog: 显示传输信息', video)
      console.log('🎬 VideoListDialog: 视频ID:', video.id, '视频名称:', video.original_name)
      console.log('🎬 VideoListDialog: 父组件:', this.$parent)
      console.log('🎬 VideoListDialog: 父组件构造函数名:', this.$parent.$options.name)
      console.log('🎬 VideoListDialog: 父组件是否有showVideoTransferInfo方法:', typeof this.$parent.showVideoTransferInfo)

      // 尝试直接调用父组件方法
      if (this.$parent && typeof this.$parent.showVideoTransferInfo === 'function') {
        console.log('🎯 VideoListDialog: 直接调用父组件的showVideoTransferInfo方法')
        this.$parent.showVideoTransferInfo(video)
        return
      }

      // 直接使用事件方式，因为组件已经正确监听了事件
      console.log('📤 VideoListDialog: 发送show-transfer-info事件')
      this.$emit('show-transfer-info', video)
      console.log('✅ VideoListDialog: 事件已发送')

      // 等待一下，然后检查是否有响应
      setTimeout(() => {
        console.log('⏰ VideoListDialog: 事件发送后1秒，检查是否有响应')
      }, 1000)
    },

    // 视频选择相关方法
    isVideoSelected(videoId) {
      return this.selectedVideoIds.includes(videoId)
    },

    toggleVideoSelection(videoId) {
      const index = this.selectedVideoIds.indexOf(videoId)
      if (index > -1) {
        this.selectedVideoIds.splice(index, 1)
      } else {
        this.selectedVideoIds.push(videoId)
      }
    },

    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedVideoIds = []
      } else {
        this.selectedVideoIds = this.filteredVideoFiles.map(video => video.id)
      }
    },

    // 批量删除视频
    async batchDeleteVideos() {
      if (this.selectedVideoIds.length === 0) {
        this.$message.warning('请先选择要删除的视频')
        return
      }

      try {
        await this.$confirm(`确定要删除选中的 ${this.selectedVideoIds.length} 个视频吗？`, '批量删除确认', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const deletePromises = this.selectedVideoIds.map(videoId => {
          return this.deleteVideoById(videoId)
        })

        await Promise.all(deletePromises)

        this.selectedVideoIds = []
        this.$message.success(`成功删除 ${deletePromises.length} 个视频`)
        await this.loadVideoFiles()

      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          this.$message.error('批量删除失败: ' + error.message)
        }
      }
    },

    // 清空所有视频
    async clearAllVideos() {
      if (this.videoFiles.length === 0) {
        this.$message.warning('没有视频可以清空')
        return
      }

      try {
        await this.$confirm(`确定要清空所有 ${this.videoFiles.length} 个视频吗？此操作不可恢复！`, '清空所有视频', {
          confirmButtonText: '确定清空',
          cancelButtonText: '取消',
          type: 'error'
        })

        // 删除所有视频
        const deletePromises = this.videoFiles.map(video => {
          return this.deleteVideoById(video.id)
        })

        await Promise.all(deletePromises)

        this.selectedVideoIds = []
        this.$message.success(`成功清空所有视频`)
        await this.loadVideoFiles()

      } catch (error) {
        if (error !== 'cancel') {
          console.error('清空视频失败:', error)
          this.$message.error('清空视频失败: ' + error.message)
        }
      }
    },

    // 删除单个视频的辅助方法
    async deleteVideoById(videoId) {
      const token = this.$store.getters['auth/token']
      // 创建一个新的axios实例，不使用全局baseURL配置
      const axiosInstance = axios.create({
        baseURL: '', // 强制使用相对路径
        timeout: axios.defaults.timeout
      })
      const response = await axiosInstance.delete(`/api/xiaohongshu/video-files/${videoId}`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      })
      return response.data
    },

    // 传输视频到设备
    async transferVideosToDevice() {
      if (this.selectedVideoIds.length === 0) {
        this.$message.warning('请先选择要传输的视频')
        return
      }

      try {
        // 获取选中的视频信息
        const selectedVideos = this.filteredVideoFiles.filter(video =>
          this.selectedVideoIds.includes(video.id)
        )

        // 显示设备选择对话框
        const deviceIds = await this.selectDevicesForTransfer()
        if (!deviceIds || deviceIds.length === 0) {
          return
        }

        this.$message.info(`开始传输 ${selectedVideos.length} 个视频到 ${deviceIds.length} 个设备...`)

        const token = this.$store.getters['auth/token']
        // 使用动态服务器地址
        const { getApiBaseUrl } = require('@/utils/serverConfig')
        const response = await axios.post(`${getApiBaseUrl()}/api/xiaohongshu/transfer-videos`, {
          deviceIds: deviceIds,
          selectedVideoIds: this.selectedVideoIds,
          selectedVideos: selectedVideos
        }, {
          headers: {
            'Content-Type': 'application/json',
            ...(token ? { 'Authorization': `Bearer ${token}` } : {})
          }
        })

        if (response.data.success) {
          this.$message.success(response.data.message)
          console.log('传输结果:', response.data.data)

          // 显示传输详情
          this.showTransferResults(response.data.data)
        } else {
          this.$message.error('传输失败: ' + response.data.message)
        }

      } catch (error) {
        console.error('传输视频失败:', error)
        this.$message.error('传输视频失败: ' + error.message)
      }
    },

    // 选择传输目标设备
    async selectDevicesForTransfer() {
      return new Promise((resolve) => {
        this.$prompt('请输入设备ID（多个设备用逗号分隔）', '选择传输设备', {
          confirmButtonText: '开始传输',
          cancelButtonText: '取消',
          inputValue: 'test-device-1',
          inputValidator: (value) => {
            if (!value || value.trim() === '') {
              return '请输入设备ID'
            }
            return true
          }
        }).then(({ value }) => {
          const deviceIds = value.split(',').map(id => id.trim()).filter(id => id)
          resolve(deviceIds)
        }).catch(() => {
          resolve(null)
        })
      })
    },

    // 显示传输结果
    showTransferResults(data) {
      const resultMessage = `
        传输任务ID: ${data.transferTaskId}
        视频数量: ${data.videoCount}
        目标设备: ${data.deviceCount}

        传输的视频:
        ${data.videos.map(v => `- ${v.name} (${this.formatFileSize(v.size)})`).join('\n')}

        设备状态:
        ${data.results.map(r => `- ${r.deviceId}: ${r.status === 'success' ? '✅' : '❌'} ${r.message}`).join('\n')}
      `

      this.$alert(resultMessage, '传输结果', {
        confirmButtonText: '确定',
        type: 'info'
      })
    },

    async deleteVideo(video) {
      try {
        await this.$confirm(`确定要删除视频文件 "${video.original_name}" 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await axios.delete(`/api/xiaohongshu/video-files/${video.id}`)
        
        if (response.data.success) {
          this.$message.success('视频文件删除成功')
          this.loadVideoFiles()
        } else {
          this.$message.error(response.data.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除视频文件失败:', error)
          this.$message.error('删除视频文件失败')
        }
      }
    },

    handleSizeChange(val) {
      this.pagination.limit = val
      this.pagination.page = 1
      this.loadVideoFiles()
    },

    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadVideoFiles()
    },

    handleClose() {
      this.visible = false
    },

    handleImageError(event) {
      try {
        if (event && event.target) {
          event.target.style.display = 'none'

          // 安全地查找父节点和no-thumbnail元素
          const parentNode = event.target.parentNode
          if (parentNode) {
            const noThumbnailElement = parentNode.querySelector('.no-thumbnail')
            if (noThumbnailElement) {
              noThumbnailElement.style.display = 'flex'
            }
          }
        }
      } catch (error) {
        console.warn('处理图片错误时出现异常:', error)
      }
    },

    getThumbnailUrl(thumbnailPath) {
      if (!thumbnailPath) return ''

      console.log('🖼️ 获取缩略图URL，输入路径:', thumbnailPath)

      // 如果已经是完整URL，直接返回
      if (thumbnailPath.startsWith('http')) {
        console.log('🖼️ 检测到完整URL，直接返回:', thumbnailPath)
        return thumbnailPath
      }

      // 使用动态服务器地址
      const { getApiBaseUrl } = require('@/utils/serverConfig')
      const baseUrl = getApiBaseUrl()

      // 确保路径以/开头
      const normalizedPath = thumbnailPath.startsWith('/') ? thumbnailPath : `/${thumbnailPath}`

      // 如果baseUrl为空（代理模式），直接返回路径；否则拼接完整URL
      const fullUrl = baseUrl ? `${baseUrl}${normalizedPath}` : normalizedPath

      console.log('🖼️ 生成缩略图URL:', fullUrl, '(baseUrl:', baseUrl, ')')
      return fullUrl
    },

    getVideoUrl(filePath) {
      if (!filePath) return ''

      console.log('🎬 获取视频URL，输入路径:', filePath)

      // 如果已经是完整URL，直接返回
      if (filePath.startsWith('http')) {
        console.log('🎬 检测到完整URL，直接返回:', filePath)
        return filePath
      }

      const { getApiBaseUrl } = require('@/utils/serverConfig')
      const baseUrl = getApiBaseUrl()

      // 如果是相对路径（以/开头），转换为绝对路径
      if (filePath.startsWith('/')) {
        const fullUrl = baseUrl ? `${baseUrl}${filePath}` : filePath
        console.log('🎬 相对路径转换为绝对路径:', fullUrl, '(baseUrl:', baseUrl, ')')
        return fullUrl
      }

      // 处理完整文件系统路径，提取文件名
      const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || filePath
      const relativePath = `/uploads/videos/${fileName}`
      const fullUrl = baseUrl ? `${baseUrl}${relativePath}` : relativePath
      console.log('🎬 从完整路径提取文件名，生成URL:', fullUrl, '(baseUrl:', baseUrl, ')')
      return fullUrl
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    formatDuration(seconds) {
      if (!seconds) return '0:00'
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins}:${secs.toString().padStart(2, '0')}`
    },

    formatTime(timeString) {
      if (!timeString) return '-'
      return new Date(timeString).toLocaleString('zh-CN')
    },

    getTransferredDevices(transferredDevicesJson) {
      if (!transferredDevicesJson) return []
      try {
        const devices = typeof transferredDevicesJson === 'string'
          ? JSON.parse(transferredDevicesJson)
          : transferredDevicesJson

        // 添加调试信息
        console.log('🔍 [VideoListDialog] 解析传输设备信息:', {
          原始数据: transferredDevicesJson,
          解析结果: devices,
          是否为数组: Array.isArray(devices)
        })

        return Array.isArray(devices) ? devices : []
      } catch (e) {
        console.error('解析传输设备信息失败:', e)
        return []
      }
    }
  }
}
</script>

<style scoped>
.video-list-dialog {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }

  .video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    min-height: 400px;
  }

  .video-card {
    position: relative;
    transition: all 0.3s;
    cursor: default;

    &.selected {
      border: 2px solid #409eff !important;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
    }
  }

  .selection-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px;
  }

  /* 视频预览容器 */
  .video-preview-container {
    position: relative;
    width: 100%;
    height: 200px;
    background: #f5f7fa;
  }

  .video-thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.02);

      .play-overlay {
        opacity: 1;
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .no-thumbnail {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #909399;

      i {
        font-size: 32px;
        margin-bottom: 8px;
      }
    }

    .play-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      opacity: 0;
      transition: opacity 0.2s;

      .play-button {
        width: 50px;
        height: 50px;
        font-size: 20px;
        background: rgba(64, 158, 255, 0.9);
        border: none;

        &:hover {
          background: rgba(64, 158, 255, 1);
          transform: scale(1.1);
        }
      }
    }

    .duration-badge {
      position: absolute;
      bottom: 8px;
      right: 8px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
    }
  }

  /* 内嵌视频播放器样式 */
  .video-player-container {
    position: relative;
    width: 100%;
    height: 100%;
    background: #000;

    .inline-video-player {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .video-controls {
      position: absolute;
      bottom: 8px;
      right: 8px;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 4px;
      padding: 4px;
    }
  }

  .video-info {
    .video-title {
      font-weight: 500;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      color: #303133;
    }

    .video-meta {
      margin-bottom: 8px;

      .el-tag {
        margin-right: 4px;
      }
    }

    .video-stats, .transfer-stats {
      margin-bottom: 8px;

      .stat-item {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #606266;

        i {
          margin-right: 4px;
          color: #909399;
        }
      }
    }

    .transferred-devices {
      .devices-label {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;

        i {
          margin-right: 4px;
          color: #909399;
        }
      }
      color: #c0c4cc;

      .upload-time {
        display: block;
        margin-bottom: 4px;
      }
    }

    .transfer-stats {
      font-size: 11px;
      color: #67c23a;
      margin-top: 4px;

      .transfer-count, .success-count {
        display: inline-block;
        margin-right: 12px;
        margin-bottom: 2px;

        i {
          margin-right: 4px;
        }
      }

      .success-count {
        color: #409eff;
      }

      .last-transfer {
        display: block;
        color: #909399;
        font-size: 10px;
      }
    }

    .transferred-devices {
      margin-top: 6px;

      .devices-label {
        font-size: 10px;
        color: #606266;
        display: block;
        margin-bottom: 4px;
      }

      .el-tag {
        margin-right: 4px;
        margin-bottom: 2px;
        font-size: 10px;
        height: 18px;
        line-height: 16px;
      }
    }
  }

  .video-actions {
    padding: 8px 12px;
    border-top: 1px solid #f0f2f5;
    display: flex;
    gap: 8px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }

  .upload-section {
    text-align: center;

    .upload-buttons {
      display: flex;
      gap: 15px;
      justify-content: center;
      margin-bottom: 15px;
      align-items: center;
    }

    .upload-tips {
      text-align: left;
      margin-top: 10px;
      font-size: 13px;
      color: #606266;
    }
  }

  .upload-progress {
    margin-top: 15px;
  }

  .video-preview {
    .preview-video {
      margin-bottom: 20px;
    }

    .preview-info {
      h3 {
        margin-bottom: 15px;
        color: #303133;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            margin-right: 8px;
            min-width: 80px;
            color: #606266;
          }

          span {
            color: #303133;
          }
        }
      }
    }
  }
}
</style>
